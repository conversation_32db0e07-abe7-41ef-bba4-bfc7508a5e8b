using MediatR;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Command handler to handle update of assessment strategies using CreateOrUpdateAssessmentIndicatorsCommand
    /// </summary>
    public class CreateOrUpdateAssessmentIndicatorsCommandHandler : RuleBase, IRequestHandler<CreateOrUpdateAssessmentIndicatorsCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly IDbManager _dbManager;
        private readonly ITranslationService _translationService;

        public CreateOrUpdateAssessmentIndicatorsCommandHandler(IMediator mediator,
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IAssessmentRuleChecker assessmentRuleChecker,
            IDbManager dbManager,
            ITranslationService translationService)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assessmentRuleChecker;
            _dbManager = dbManager;
            _translationService = translationService;
        }

        /// <summary>
        /// Adds/Updates/Deletes assessment indicators based on the list of indicators passed
        /// </summary>
        /// <param name="request">Command includes properties related to assessment indicators saving operation</param>
        /// <param name="cancellationToken">Notify the cancellation request</param>
        /// <returns>Returns true if records are created</returns>
        public async Task<bool> Handle(CreateOrUpdateAssessmentIndicatorsCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.AssessmentId, "AssessmentId"));
            CheckRule(new GuidArrayNotNullOrEmptyRule(_translationService, _commonRuleChecker, request.IndicatorIds, "IndicatorIds"));
            CheckRule(new ValidIndicatorSelectionForAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.IndicatorIds));
            CheckRule(new AssessmentShouldExistRule(_translationService, _assessmentRuleChecker, request.AssessmentId));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanUpdateIndicators));

            if (request.AssessmentApproach != (int)AssessmentApproach.Rapid)
            {
                CheckRule(new AtleastOneOptionalIndicatorMustBeSelectedRule(_translationService, _assessmentRuleChecker, request.IndicatorIds, request.AssessmentId));
            }

            DataTable assessmentIndicatorsTable = DatabaseHelper.BuildUniqueidentifierCollectionDataTable(request.IndicatorIds);

            await _dbManager.ExecuteAsync($"{MalariaSchemas.ScopeDefinition}.SaveAssessmentApproachAndIndicators",
             new
             {
                 AssessmentId = request.AssessmentId,
                 AssessmentApproach = request.AssessmentApproach,
                 IndicatorIds = assessmentIndicatorsTable,
                 CurrentUserId = request.CurrentUserId
             }, null, null, CommandType.StoredProcedure);

            return true;
        }
    }
}
