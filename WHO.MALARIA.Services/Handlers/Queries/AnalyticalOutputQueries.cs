using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.DocumentManager;
using WHO.MALARIA.DocumentManager.Models;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Dtos.InputDtos;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.SeedingMetadata;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;

namespace WHO.MALARIA.Services.Handlers.Queries
{
    /// <summary>
    /// Provides queries to fetch data for assessment survey questions
    /// </summary>
    public class AnalyticalOutputQueries : RuleBase, IAnalyticalOutputQueries
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAssessmentQueries _assessmentQueries;
        private readonly ITranslationService _translationService;
        private readonly IAnalyticalOutput _analyticalOutputDocumentManager;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;
        private readonly ILogger<AnalyticalOutputQueries> _logger;
        public delegate string CommonTranslator(string key, string fileName);
        public delegate string Translator(string key);
        public delegate JObject ParentResponse(Guid assessmentId, Guid assessmentStrategyId, Guid parentIndicatorId);
        public delegate IEnumerable<ResponseDocumentDto> ParentDocuments(Guid assessmentId, Guid assessmentStrategyId, Guid parentIndicatorId);

        public AnalyticalOutputQueries(IUnitOfWork unitOfWork,
                                       IAssessmentQueries assessmentQueries,
                                       ITranslationService translationService,
                                       IAnalyticalOutput analyticalOutputDocumentManager,
                                       ICommonRuleChecker commonRuleChecker,
                                       IAssessmentRuleChecker assessmentRuleChecker,
                                       ILogger<AnalyticalOutputQueries> logger)
        {
            _unitOfWork = unitOfWork;
            _assessmentQueries = assessmentQueries;
            _translationService = translationService;
            _analyticalOutputDocumentManager = analyticalOutputDocumentManager;
            _commonRuleChecker = commonRuleChecker;
            _assessmentRuleChecker = assessmentRuleChecker;
            _logger = logger;
        }

        #region Public Method

        /// <summary>
        /// Fetch objective diagrams based on the assessmentId and strategyId
        /// </summary>
        /// <param name="assessmentId">Required to fetch association of assessment with the strategy</param>
        /// <param name="strategyId">Required to fetch the association of strategy with the assessment</param>        
        /// <returns>Objective diagrams and other details</returns>
        public async Task<IEnumerable<FileDto>> GetObjectiveDiagramsAsync(Guid assessmentId, Guid strategyId)
        {
            ObjectiveDiagramDto objectiveDiagramDto = await _unitOfWork.AssessmentRepository.GetObjectiveDiagramsAsync(assessmentId, strategyId);

            List<FileDto> objectiveDiagrams = new List<FileDto>();
            if (objectiveDiagramDto != null)
            {
                foreach (DiagramDetailDto objectiveDiagram in objectiveDiagramDto.DiagramDetails)
                {
                    FileDto fileDto = new FileDto();
                    fileDto.File = objectiveDiagram.File;
                    fileDto.Order = objectiveDiagram.Order;
                    fileDto.Extension = objectiveDiagram.Extension;
                    fileDto.FileName = objectiveDiagram.FileName;
                    objectiveDiagrams.Add(fileDto);
                }
            }
            return objectiveDiagrams.OrderBy(o => o.Order);
        }

        /// <summary>
        /// Get collection of strategies, indicators, sub-objectives and objectives information that are associated with the analytical output and assessment
        /// </summary>
        /// <param name="assessmentId">Assessment id for which analytical output strategies, objectives, sub-objectives, indicators are to be fetched</param>
        /// <returns>Collection of analytical output indicators, sub-objectives, objectives, strategies</returns>
        public async Task<AnalyticalOutputDetailDto> GetAnalyticalOutputDetailsAsync(Guid assessmentId)
        {
            if (assessmentId == Guid.Empty)
            {
                return null;
            }

            AnalyticalOutputDetailDto analyticalOutputDetail = new AnalyticalOutputDetailDto();

            IEnumerable<AnalyticalOutputDto> analyticalOutputs = await _unitOfWork.AnalyticalOutputRepository.GetStrategyObjectivesSubObjectivesIndicatorsAsync(assessmentId);

            analyticalOutputDetail.Strategies = analyticalOutputs.OrderBy(t => t.StrategyOrder).GroupBy(x => new { x.StrategyId, x.StrategyName })
                                                                 .Select(x => new AnalyticalOutputStrategyDto
                                                                 {
                                                                     Id = x.Key.StrategyId,
                                                                     Name = x.Key.StrategyName
                                                                 }).ToList();

            analyticalOutputDetail.Objectives = analyticalOutputs.GroupBy(x => new { x.StrategyId, x.ObjectiveId, x.ObjectiveName, x.ObjectiveSequence })
                                                                 .Select(x => new AnalyticalOutputObjectiveDto
                                                                 {
                                                                     StrategyId = x.Key.StrategyId,
                                                                     Id = x.Key.ObjectiveId,
                                                                     Name = x.Key.ObjectiveName,
                                                                     Sequence = x.Key.ObjectiveSequence
                                                                 }).ToList();

            analyticalOutputDetail.SubObjectives = analyticalOutputs.GroupBy(x => new { x.StrategyId, x.SubObjectiveId, x.SubObjectiveName, x.SubObjectiveSequence, x.ObjectiveId })
                                                                    .Select(x => new AnalyticalOutputSubObjectiveDto
                                                                    {
                                                                        StrategyId = x.Key.StrategyId,
                                                                        Id = x.Key.SubObjectiveId,
                                                                        Name = x.Key.SubObjectiveName,
                                                                        Sequence = x.Key.SubObjectiveSequence,
                                                                        ObjectiveId = x.Key.ObjectiveId
                                                                    }).ToList();

            analyticalOutputDetail.Indicators = analyticalOutputs.GroupBy(x => new { x.StrategyId, x.IndicatorId, x.IndicatorName, x.IndicatorSequence, x.SubObjectiveId, x.AssessmentIndicatorId, x.AssessmentStrategyId })
                                                                 .Select(x => new AnalyticalOutputIndicatorDto
                                                                 {
                                                                     StrategyId = x.Key.StrategyId,
                                                                     Id = x.Key.IndicatorId,
                                                                     Name = x.Key.IndicatorName,
                                                                     Sequence = x.Key.IndicatorSequence,
                                                                     SubObjectiveId = x.Key.SubObjectiveId,
                                                                     AssessmentIndicatorId = x.Key.AssessmentIndicatorId,
                                                                     AssessmentStrategyId = x.Key.AssessmentStrategyId
                                                                 }).ToList();

            return analyticalOutputDetail;
        }

        /// <summary>
        /// Get translation for the given key and file name
        /// </summary>
        /// <param name="key">Key as a string</param>
        /// /// <param name="fileName">file name as a string</param>
        /// <returns>Translated text</returns>
        public string GetTranslationFromFile(string key, string fileName)
        {
            return _translationService.GetTranslation(key, fileName);
        }

        /// <summary>
        /// Get translation for the given key 
        /// </summary>
        /// <param name="key">Key as a string</param>
        /// <returns>Translated text</returns>
        public string GetTranslation(string key)
        {
            if (string.IsNullOrEmpty(key))
                return key;

            return _translationService.GetTranslation(key, "indicators-responses");
        }

        /// <summary>
        /// Get analytical output response for indicator 
        /// </summary>
        /// <param name="AnalyticalOutputIndicatorResponseInputDto">Contains input based object for analytical output reponse indicator</param>
        /// <returns>Returns analytical output response for indicator</returns>
        public async Task<dynamic> GetAnalyticalOutputIndicatorResponseAsync(AnalyticalOutputIndicatorResponseInputDto request)
        {
            if (request.AssessmentId == Guid.Empty || request.IndicatorId == Guid.Empty
                || request.StrategyId == Guid.Empty || request.AssessmentIndicatorId == Guid.Empty || request.AssessmentStrategyId == Guid.Empty)
            {
                return null;
            }

            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanViewDetails));

            Type responseType;
            try
            {
                responseType = DeskReviewResponseHelper.GetResponseType(request.IndicatorId, request.StrategyId);
            }
            catch (KeyNotFoundException ex)
            {
                _logger.LogInformation(this.GetType().Name, MethodBase.GetCurrentMethod().Name, $"Key not found= {ex.Message}");
                throw new KeyNotFoundException(_translationService.GetTranslatedMessage(Constants.Exception.ResponseMappingKeyIsNotFound));
            }

            Guid[] indicators = { request.IndicatorId };

            IEnumerable<ResponseDocumentDto> documentLists = GetDocumentList(indicators, request.AssessmentIndicatorId, request.AssessmentStrategyId);

            IEnumerable<DRIndicatorCheckListDto> drIndicatorCheckLists = GetDRIndicatorCheckList(indicators, request.StrategyId);

            IEnumerable<DRVariableCheckListDto> drVariableCheckLists = GetDRVariableCheckList(indicators, request.StrategyId);

            JObject responseString = _assessmentQueries.GetDeskReviewResponse(request.AssessmentIndicatorId, request.AssessmentStrategyId);

            JObject responseObject = JObject.Parse(responseString.ToString());

            object responseJsonObject = JsonSerializer.Deserialize(responseObject.ToString(), responseType, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            Translator translator = new Translator(GetTranslation);

            ParentResponse parentResponse = new ParentResponse(GetParentResponse);

            ParentDocuments parentDocuments = new ParentDocuments(GetParentDocuments);

            object[] parameters = GetResponseParameters(translator, parentResponse, parentDocuments, request.IndicatorId, request.StrategyId, request.AssessmentId, request.AssessmentStrategyId, false, "", documentLists, drIndicatorCheckLists, drVariableCheckLists);

            MethodInfo reportDataObjectResult = responseType.GetMethod("BuildReportResponse");

            dynamic reportData = reportDataObjectResult.Invoke(responseJsonObject, parameters);

            return reportData;
        }

        /// <summary>
        /// Create a analytical output template
        /// </summary>
        /// <param name="request">object of AnalyticalOutputReportCommand</param>     
        /// <returns>file download response with file byte[] and file name</returns>
        public async Task<FileResponseDto> CreateAnalyticalOutputReportTemplateAsync(AnalyticalOutputReportRequestModel request)
        {
            if (!request.analyticalOutputIndicators.Any() || request.StrategyId == Guid.Empty || request.AssessmentId == Guid.Empty)
            {
                return null;
            }

            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanViewDetails));

            FileResponseDto fileResponseDto = new FileResponseDto();

            IEnumerable<AssessmentDeskReviewResponseDto> assessmentDeskReviewResponses = await _unitOfWork.AssessmentDRResponseRepository.GetAssessmentResponseByAssessmentIdAndStrategyId(request.AssessmentId, request.StrategyId);

            Guid[] indicators = request.analyticalOutputIndicators.Select(a => a.IndicatorId).ToArray();

            IEnumerable<DRIndicatorCheckListDto> drIndicatorCheckLists = GetDRIndicatorCheckList(indicators, request.StrategyId);

            IEnumerable<DRVariableCheckListDto> drVariableCheckLists = GetDRVariableCheckList(indicators, request.StrategyId);

            List<ChartInputModel> chartData = new List<ChartInputModel>();

            List<TabularDataInputModel> tabularData = new List<TabularDataInputModel>();

            foreach (AnalyticalOutputIndicator analyticalOutput in request.analyticalOutputIndicators)
            {
                Type responseType;
                try
                {
                    responseType = DeskReviewResponseHelper.GetResponseType(analyticalOutput.IndicatorId, request.StrategyId);
                }
                catch (KeyNotFoundException ex)
                {
                    _logger.LogInformation(this.GetType().Name, MethodBase.GetCurrentMethod().Name, $"Key not found= {ex.Message}");
                    throw new KeyNotFoundException(_translationService.GetTranslatedMessage(Constants.Exception.ResponseMappingKeyIsNotFound));
                }

                string indicatorSequence = assessmentDeskReviewResponses.FirstOrDefault(i => i.AssessmentIndicatorId == analyticalOutput.AssessmentIndicatorId && i.AssessmentStrategyId == analyticalOutput.AssessmentStrategyId)?.IndicatorSequence;

                IEnumerable<ResponseDocumentDto> documents = assessmentDeskReviewResponses.Where(i => i.AssessmentIndicatorId == analyticalOutput.AssessmentIndicatorId && i.AssessmentStrategyId == analyticalOutput.AssessmentStrategyId)
                                                                                          .Select(item =>
                                                                                                   new ResponseDocumentDto
                                                                                                   {
                                                                                                       FileName = item.FileName,
                                                                                                       Id = item.DocumentId
                                                                                                   }
                                                                                                  );

                string responseString = assessmentDeskReviewResponses.Where(i => i.AssessmentIndicatorId == analyticalOutput.AssessmentIndicatorId &&
                                                                                 i.AssessmentStrategyId == analyticalOutput.AssessmentStrategyId)
                                                                     .FirstOrDefault()?.ResponseJson;

                JObject responseObject = JObject.Parse(responseString == null ? string.Empty : responseString);

                dynamic responseJsonObject = JsonSerializer.Deserialize(responseObject.ToString(), responseType, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                MethodInfo reportDataObjectResult = responseType.GetMethod("BuildAnalyticalReport");

                Translator translator = new Translator(GetTranslation);

                ParentResponse parentResponse = new ParentResponse(GetParentResponse);

                ParentDocuments parentDocuments = new ParentDocuments(GetParentDocuments);

                object[] parameters = GetResponseParameters(translator, parentResponse, parentDocuments, analyticalOutput.IndicatorId, request.StrategyId, request.AssessmentId, analyticalOutput.AssessmentStrategyId, true, indicatorSequence, documents, drIndicatorCheckLists, drVariableCheckLists);

                dynamic reportData = reportDataObjectResult.Invoke(responseJsonObject, parameters);

                switch (reportData)
                {
                    case ChartInputModel model:

                        chartData.Add(reportData);
                        break;
                    case List<ChartInputModel> model:

                        chartData.AddRange(reportData);
                        break;
                    case TabularDataInputModel model:

                        tabularData.Add(reportData);
                        break;
                    case List<TabularDataInputModel> model:

                        tabularData.AddRange(reportData);
                        break;
                    default:

                        break;
                }
            }

            fileResponseDto.FileData = _analyticalOutputDocumentManager.AddDataInTemplate(chartData, tabularData);

            fileResponseDto.FileName = $"{Constants.DownloadDocument.AnalyticalOutputTemplateFileName}.xlsx";

            return fileResponseDto;
        }

        /// <summary>
        /// Get parent response
        /// </summary>
        /// <param name="assessmentId">Assessment id</param>
        /// <param name="assessmentStrategyId">Assessment strategy id</param>
        /// <param name="parentIndicatorId">Parent indicator id</param>
        /// <returns>Parent response object</returns>
        public JObject GetParentResponse(Guid assessmentId, Guid assessmentStrategyId, Guid parentIndicatorId)
        {
            Guid indicatorId = _unitOfWork.AssessmentDRResponseRepository.GetAssessmentIndicatorIdByAssesmentIdAndIndicatorId(assessmentId, parentIndicatorId);

            JObject response = _unitOfWork.AssessmentDRResponseRepository.GetResponse(indicatorId, assessmentStrategyId);

            return response;
        }

        /// <summary>
        /// Get parent response documents
        /// </summary>
        /// <param name="assessmentId">Assessment id</param>
        /// <param name="assessmentStrategyId">Assessment strategy id</param>
        /// <param name="parentIndicatorId">Parent indicator id</param>
        /// <returns>Document list</returns>
        public IEnumerable<ResponseDocumentDto> GetParentDocuments(Guid assessmentId, Guid assessmentStrategyId, Guid parentIndicatorId)
        {
            Guid indicatorId = _unitOfWork.AssessmentDRResponseRepository.GetAssessmentIndicatorIdByAssesmentIdAndIndicatorId(assessmentId, parentIndicatorId);

            IEnumerable<ResponseDocumentDto> documentList = _unitOfWork.ResponseDocumentRepository.GetAsync(indicatorId, assessmentStrategyId).Result;

            return documentList;
        }
        #endregion

        #region Private Method

        /// <summary>
        /// Add indicator sequence parameters
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>    
        /// <param name="parentResponse">Delegate object which is used for parent response</param>  
        /// <param name="parentDocuments">Delegate object which is used for parent document</param>  
        /// <param name="indicatorId">Indicator id</param>
        /// <param name="strategyId">Strategy id</param>
        /// <param name="assessmentId">Assessment id</param>
        /// <param name="assessmentStrategyId">Assessment strategy id</param>      
        /// <param name="addIndicatorSequenceParam">Add indicator sequence parameters</param>
        /// <param name="indicatorSequence">Indicator sequence</param>
        /// <param name="documents">Document list</param>
        /// <param name="drIndicatorCheckLists">Desk review indicator check list</param>
        /// <param name="drVariableCheckLists">Desk review variable check list</param>
        /// <returns>Object array</returns>
        private object[] GetResponseParameters(Translator translator, ParentResponse parentResponse, ParentDocuments parentDocuments, Guid indicatorId, Guid strategyId, Guid assessmentId, Guid assessmentStrategyId, bool addIndicatorSequenceParam, string indicatorSequence, IEnumerable<ResponseDocumentDto> documents = null, IEnumerable<DRIndicatorCheckListDto> drIndicatorCheckLists = null, IEnumerable<DRVariableCheckListDto> drVariableCheckLists = null)
        {
            List<object> objectParam = new List<object>();

            objectParam.Add(translator);

            if (addIndicatorSequenceParam)
                objectParam.Add(indicatorSequence);

            switch (indicatorId)
            {
                case Guid IND_3_2_1 when (IND_3_2_1 == IndicatorSeedingMetadata.IND_3_2_1):
                case Guid IND_3_4_1 when (IND_3_4_1 == IndicatorSeedingMetadata.IND_3_4_1):

                    objectParam.Add(documents);
                    break;

                case Guid IND_3_4_2 when (IND_3_4_2 == IndicatorSeedingMetadata.IND_3_4_2):

                    objectParam.Add(drIndicatorCheckLists);
                    break;

                case Guid IND_3_2_2 when (IND_3_2_2 == IndicatorSeedingMetadata.IND_3_2_2):

                    objectParam.Add(drVariableCheckLists);
                    objectParam.Add(strategyId);
                    break;

                case Guid IND_3_3_2 when (IND_3_3_2 == IndicatorSeedingMetadata.IND_3_3_2):

                    objectParam.Add(drVariableCheckLists);
                    break;

                case Guid IND_3_3_1 when (IND_3_3_1 == IndicatorSeedingMetadata.IND_3_3_1):

                    objectParam.Add(parentResponse);
                    objectParam.Add(parentDocuments);
                    objectParam.Add(documents);
                    objectParam.Add(assessmentId);
                    objectParam.Add(assessmentStrategyId);
                    break;

                case Guid IND_2_1_3 when (IND_2_1_3 == IndicatorSeedingMetadata.IND_2_1_3) && strategyId != StrategySeedingMetadata.BURDEN_REDUCTION_ID:

                    objectParam.Add(strategyId);

                    break;

                case Guid IND_1_3_5 when (IND_1_3_5 == IndicatorSeedingMetadata.IND_1_3_5):
                case Guid IND_1_3_6 when (IND_1_3_6 == IndicatorSeedingMetadata.IND_1_3_6):
                    if (addIndicatorSequenceParam)
                    {
                        CommonTranslator commonTranslator = new CommonTranslator(GetTranslationFromFile);
                        objectParam.Add(commonTranslator);
                    }
                    break;
            }

            return objectParam.ToArray();
        }

        /// <summary>
        /// Get desk review document List 
        /// </summary>
        /// <param name="indicators">Indicators list</param>
        /// <param name="assessmentIndicatorId">Assessment indicator id</param>
        /// /// <param name="assessmentStrategyId">Assessment strategy id</param>
        /// <returns>Desk review document list</returns>
        private IEnumerable<ResponseDocumentDto> GetDocumentList(Guid[] indicators, Guid assessmentIndicatorId, Guid assessmentStrategyId)
        {
            IEnumerable<ResponseDocumentDto> documentList = null;

            Guid[] documentIndicators = { IndicatorSeedingMetadata.IND_3_2_1, IndicatorSeedingMetadata.IND_3_3_1, IndicatorSeedingMetadata.IND_3_4_1 };

            if (indicators.Intersect(documentIndicators).Any())
            {
                documentList = _unitOfWork.ResponseDocumentRepository.GetAsync(assessmentIndicatorId, assessmentStrategyId).Result;
            }

            return documentList;
        }

        /// <summary>
        /// Get desk review indicators check List 
        /// </summary>
        /// <param name="indicators">Indicators list</param>
        /// <param name="strategyId">Strategy id</param>
        /// <returns>Desk review indicators check list</returns>
        private IEnumerable<DRIndicatorCheckListDto> GetDRIndicatorCheckList(Guid[] indicators, Guid strategyId)
        {
            IEnumerable<DRIndicatorCheckListDto> drIndicatorCheckLists = null;

            Guid[] drIndicators = { IndicatorSeedingMetadata.IND_3_4_2 };

            if (indicators.Intersect(drIndicators).Any())
            {
                drIndicatorCheckLists = _unitOfWork.AssessmentRepository.GetDeskReviewIndicatorsChecklistAsync(strategyId).Result;
            }

            return drIndicatorCheckLists;
        }

        /// <summary>
        /// Get desk review variable check List 
        /// </summary>
        /// <param name="indicators">Indicators list</param>
        /// <param name="strategyId">Strategy id</param>
        /// <returns>Desk review check list</returns>
        private IEnumerable<DRVariableCheckListDto> GetDRVariableCheckList(Guid[] indicators, Guid strategyId)
        {
            IEnumerable<DRVariableCheckListDto> drVariableCheckLists = null;

            Guid[] drVariableIndicators = { IndicatorSeedingMetadata.IND_3_3_2, IndicatorSeedingMetadata.IND_3_2_2 };

            if (indicators.Intersect(drVariableIndicators).Any())
            {
                drVariableCheckLists = _unitOfWork.AssessmentRepository.GetDRVariablesAsync(strategyId).Result;
            }

            return drVariableCheckLists;
        }
        #endregion
    }
}
