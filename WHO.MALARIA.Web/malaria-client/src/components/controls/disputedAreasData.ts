export const disputedAreasData = {
    type: "Topology",
    objects: {
        disputed_areas: {
            type: "GeometryCollection",
            geometries: [
                // Example: Disputed area between two countries
                {
                    type: "Polygon",
                    arcs: [[0, 1, 2, 3]],
                    properties: { name: "Disputed Area 1" }
                }
            ]
        }
    },
    arcs: [
        [[10, 10], [10, 20]],
        [[10, 20], [20, 20]],
        [[20, 20], [20, 10]],
        [[20, 10], [10, 10]]
    ]
};