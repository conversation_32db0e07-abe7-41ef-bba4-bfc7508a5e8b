﻿import { DataType } from '../../../../../../../models/Enums';
import { Constants } from '../../../../../../../models/Constants';
import ValidationRuleModel, {
  IValidationRuleProvider,
} from '../../../../../../../models/ValidationRuleModel';

const ValidationRules: IValidationRuleProvider = {
  hasPVivaxCases: new ValidationRuleModel(DataType.Boolean, true),
  transmitMalariaIndicators: new ValidationRuleModel(
    DataType.ArrayOfObject,
    true
  ),
  [`transmitMalariaIndicators[${Constants.Common.IndexSubstitute}].indicatorMonitored`]:
    new ValidationRuleModel(
      DataType.String,
      false,
      `(${Constants.Common.RootObjectNameSubstitute}.transmitMalariaIndicators.some(data => data.indicatorMonitored === null) || (${Constants.Common.RootObjectNameSubstitute}.checkListIndicatorsCount !== ${Constants.Common.RootObjectNameSubstitute}.transmitMalariaIndicators.length))`
    ),
};

export default ValidationRules;
