import { Objective } from '../useIndicatorResponseLoader';

import UserMessage from '../../../../../common/UserMessage';
import Indicator_1_1_1_Response from '../../case_strategies/objective_1/indicator_1_1_1/Indicator_1_1_1_Response';
import Indicator_1_1_2_Response from '../../case_strategies/objective_1/indicator_1_1_2/Indicator_1_1_2_Response';
import Indicator_1_1_3_Response from '../../case_strategies/objective_1/indicator_1_1_3/Indicator_1_1_3_Response';
import Indicator_1_1_4_Response from '../../case_strategies/objective_1/indicator_1_1_4/Indicator_1_1_4_Response';
import Indicator_1_1_5_Response from '../../case_strategies/objective_1/indicator_1_1_5/Indicator_1_1_5_Response';
import Indicator_1_1_6_Response from '../../case_strategies/objective_1/indicator_1_1_6/Indicator_1_1_6_Response';
import Indicator_1_1_7_ResponseContainer from '../../case_strategies/objective_1/indicator_1_1_7/Indicator_1_1_7_ResponseContainer';
import Indicator_1_1_8_Response from '../../case_strategies/objective_1/indicator_1_1_8/Indicator_1_1_8_Response';
import Indicator_1_1_9_Response from '../../case_strategies/objective_1/indicator_1_1_9/Indicator_1_1_9_Response';
import Indicator_1_3_1_Response from '../../case_strategies/objective_1/indicator_1_3_1/Indicator_1_3_1_Response';
import Indicator_1_3_2_Response from '../../case_strategies/objective_1/indicator_1_3_2/Indicator_1_3_2_Response';
import Indicator_1_3_3_Response from '../../case_strategies/objective_1/indicator_1_3_3/Indicator_1_3_3_Response';
import Indicator_1_3_4_ResponseContainer from '../../case_strategies/objective_1/indicator_1_3_4/Indicator_1_3_4_ResponseContainer';
import Indicator_1_3_5_Response from '../../case_strategies/objective_1/indicator_1_3_5/Indicator_1_3_5_Response';
import Indicator_1_3_6_Response from '../../case_strategies/objective_1/indicator_1_3_6/Indicator_1_3_6_Response';
import Indicator_1_3_7_Response from '../../case_strategies/objective_1/indicator_1_3_7/Indicator_1_3_7_Response';
import Indicator_2_1_1_Response from '../../case_strategies/objective_2/indicator_2_1_1/Indicator_2_1_1_Response';
import Indicator_2_1_2_Response from '../../case_strategies/objective_2/indicator_2_1_2/Indicator_2_1_2_Response';
import Indicator_2_1_3_Response from '../../case_strategies/objective_2/indicator_2_1_3/Indicator_2_1_3_Response';
import Indicator_2_1_4_Response from '../../case_strategies/objective_2/indicator_2_1_4/Indicator_2_1_4_Response';
import Indicator_2_1_4_Both_Response from '../../case_strategies/objective_2/indicator_2_1_4/Indicator_2_1_4_Both_Response';
import Indicator_2_1_4_Burden_Response from '../../case_strategies/objective_2/indicator_2_1_4/Indicator_2_1_4_Burden_Response';
import Indicator_2_2_1_Response from '../../case_strategies/objective_2/indicator_2_2_1/Indicator_2_2_1_Response';
import Indicator_2_2_2_ResponseContainer from '../../case_strategies/objective_2/indicator_2_2_2/Indicator_2_2_2_ResponseContainer';
import Indicator_2_2_3_Response from '../../case_strategies/objective_2/indicator_2_2_3/Indicator_2_2_3_Response';
import Indicator_2_2_4_Response from '../../case_strategies/objective_2/indicator_2_2_4/Indicator_2_2_4_Response';
import Indicator_2_2_5_Response from '../../case_strategies/objective_2/indicator_2_2_5/Indicator_2_2_5_Response';
import Indicator_2_2_6_Response from '../../case_strategies/objective_2/indicator_2_2_6/Indicator_2_2_6_Response';
import Indicator_2_3_1_Response from '../../case_strategies/objective_2/indicator_2_3_1/Indicator_2_3_1_Response';
import Indicator_2_3_2_Response from '../../case_strategies/objective_2/indicator_2_3_2/Indicator_2_3_2_Response';
import Indicator_2_4_1_Response from '../../case_strategies/objective_2/indicator_2_4_1/Indicator_2_4_1_Response';
import Indicator_2_4_2_Response from '../../case_strategies/objective_2/indicator_2_4_2/Indicator_2_4_2_Response';
import Indicator_2_4_4_Response from '../../case_strategies/objective_2/indicator_2_4_4/Indicator_2_4_4_Response';
import Indicator_2_5_1_Response from '../../case_strategies/objective_2/indicator_2_5_1/Indicator_2_5_1_Response';
import Indicator_3_1_2_ResponseContainer from '../../case_strategies/objective_3/indicator_3_1_2/Indicator_3_1_2_ResponseContainer';
import Indicator_3_1_2_Burden_ResponseContainer from '../../case_strategies/objective_3/indicator_3_1_2/Indicator_3_1_2_Burden_ResponseContainer';
import Indicator_3_1_3_Response from '../../case_strategies/objective_3/indicator_3_1_3/Indicator_3_1_3_Response';
import Indicator_3_2_1_Response from '../../case_strategies/objective_3/indicator_3_2_1/Indicator_3_2_1_Response';
import Indicator_3_2_2_ResponseContainer from '../../case_strategies/objective_3/indicator_3_2_2/Indicator_3_2_2_ResponseContainer';
import Indicator_3_2_2_Burden_ResponseContainer from '../../case_strategies/objective_3/indicator_3_2_2/Indicator_3_2_2_Burden_ResponseContainer';
import Indicator_3_2_2_Both_ResponseContainer from '../../case_strategies/objective_3/indicator_3_2_2/Indicator_3_2_2_Both_ResponseContainer';
import Indicator_3_2_3_Response from '../../case_strategies/objective_3/indicator_3_2_3/Indicator_3_2_3_Response';
import Indicator_3_3_1_Response from '../../case_strategies/objective_3/indicator_3_3_1/Indicator_3_3_1_Response';
import Indicator_3_3_2_Response from '../../case_strategies/objective_3/indicator_3_3_2/Indicator_3_3_2_Response';
import Indicator_3_3_3_Response from '../../case_strategies/objective_3/indicator_3_3_3/Indicator_3_3_3_Response';
import Indicator_3_3_4_Response from '../../case_strategies/objective_3/indicator_3_3_4/Indicator_3_3_4_Response';
import Indicator_3_4_1_Response from '../../case_strategies/objective_3/indicator_3_4_1/Indicator_3_4_1_Response';
import Indicator_3_4_2_Response from '../../case_strategies/objective_3/indicator_3_4_2/Indicator_3_4_2_Response';
import Indicator_3_5_1_Response from '../../case_strategies/objective_3/indicator_3_5_1/Indicator_3_5_1_Response';
import Indicator_3_5_2_Response from '../../case_strategies/objective_3/indicator_3_5_2/Indicator_3_5_2_Response';
import Indicator_3_5_3_Response from '../../case_strategies/objective_3/indicator_3_5_3/Indicator_3_5_3_Response';
import Indicator_3_6_1_Response from '../../case_strategies/objective_3/indicator_3_6_1/Indicator_3_6_1_Response';
import Indicator_4_1_1_Response from '../../case_strategies/objective_4/indicator_4_1_1/Indicator_4_1_1_Response';
import Indicator_4_1_2_Response from '../../case_strategies/objective_4/indicator_4_1_2/Indicator_4_1_2_Response';
import Indicator_4_1_3_Response from '../../case_strategies/objective_4/indicator_4_1_3/Indicator_4_1_3_Response';
import Indicator_4_2_1_Response from '../../case_strategies/objective_4/indicator_4_2_1/Indicator_4_2_1_Response';
import Indicator_4_2_2_Response from '../../case_strategies/objective_4/indicator_4_2_2/Indicator_4_2_2_Response';
import Indicator_4_3_1_Response from '../../case_strategies/objective_4/indicator_4_3_1/Indicator_4_3_1_Response';
import Indicator_4_4_1_Response from '../../case_strategies/objective_4/indicator_4_4_1/Indicator_4_4_1_Response';
import Indicator_4_4_1_Burden_Response from '../../case_strategies/objective_4/indicator_4_4_1/Indicator_4_4_1_Burden_Response';
import Indicator_4_4_2_Response from '../../case_strategies/objective_4/indicator_4_4_2/Indicator_4_4_2_Response';
import Indicator_4_4_3_Response from '../../case_strategies/objective_4/indicator_4_4_3/Indicator_4_4_3_Response';
import { useLocation } from 'react-router-dom';
import { StrategiesEnum } from '../../../../../../models/Enums';
import React from 'react';

/** Renders the case strategy screens (Burden Reductio & Elimination) */
function useCaseStrategies() {
  const location: any = useLocation();
  const strategyId: string = location?.state?.strategyId;

  // get the list of objectives
  const {
    Objective1: {
      Indicator_1_1_1,
      Indicator_1_1_2,
      Indicator_1_1_3,
      Indicator_1_1_4,
      Indicator_1_1_5,
      Indicator_1_1_6,
      Indicator_1_1_7,
      Indicator_1_1_8,
      Indicator_1_1_9,
      Indicator_1_3_1,
      Indicator_1_3_2,
      Indicator_1_3_3,
      Indicator_1_3_4,
      Indicator_1_3_5,
      Indicator_1_3_6,
      Indicator_1_3_7,
    },
    Objective2: {
      Indicator_2_1_1,
      Indicator_2_1_2,
      Indicator_2_1_3,
      Indicator_2_1_4,
      Indicator_2_2_1,
      Indicator_2_2_2,
      Indicator_2_2_3,
      Indicator_2_2_4,
      Indicator_2_2_5,
      Indicator_2_2_6,
      Indicator_2_3_1,
      Indicator_2_3_2,
      Indicator_2_4_1,
      Indicator_2_4_2,
      Indicator_2_4_4,
      Indicator_2_5_1,
    },
    Objective3: {
      Indicator_3_1_2,
      Indicator_3_1_3,
      Indicator_3_2_1,
      Indicator_3_2_2,
      Indicator_3_2_3,
      Indicator_3_3_1,
      Indicator_3_3_2,
      Indicator_3_3_4,
      Indicator_3_4_2,
      Indicator_3_3_3,
      Indicator_3_4_1,
      Indicator_3_5_1,
      Indicator_3_5_2,
      Indicator_3_5_3,
      Indicator_3_6_1,
    },
    Objective4: {
      Indicator_4_1_1,
      Indicator_4_1_2,
      Indicator_4_1_3,
      Indicator_4_2_1,
      Indicator_4_2_2,
      Indicator_4_3_1,
      Indicator_4_4_1,
      Indicator_4_4_2,
      Indicator_4_4_3,
    },
  } = Objective;

  /** Renders the response of the indicators
   * @param sequence A string variable of an indicator's sequence like 1.1.1, 1.1.2 and so on
   * @param label A string variable which represents indicator's name
   */
  const renderResponse = (sequence: string, label: string) => {
    switch (sequence) {
      // Objective 1
      case Indicator_1_1_1:
        return <Indicator_1_1_1_Response />;
      case Indicator_1_1_2:
        return <Indicator_1_1_2_Response />;
      case Indicator_1_1_3:
        return <Indicator_1_1_3_Response />;
      case Indicator_1_1_4:
        return <Indicator_1_1_4_Response />;
      case Indicator_1_1_5:
        return <Indicator_1_1_5_Response />;
      case Indicator_1_1_6:
        return <Indicator_1_1_6_Response />;
      case Indicator_1_1_7:
        return <Indicator_1_1_7_ResponseContainer />;
      case Indicator_1_1_8:
        return <Indicator_1_1_8_Response />;
      case Indicator_1_1_9:
        return <Indicator_1_1_9_Response />;
      case Indicator_1_3_1:
        return <Indicator_1_3_1_Response />;
      case Indicator_1_3_2:
        return <Indicator_1_3_2_Response />;
      case Indicator_1_3_3:
        return <Indicator_1_3_3_Response />;
      case Indicator_1_3_4:
        return <Indicator_1_3_4_ResponseContainer />;
      case Indicator_1_3_5:
        return <Indicator_1_3_5_Response />;
      case Indicator_1_3_6:
        return <Indicator_1_3_6_Response />;
      case Indicator_1_3_7:
        return <Indicator_1_3_7_Response />;

      // Objective 2
      case Indicator_2_1_1:
        return <Indicator_2_1_1_Response />;
      case Indicator_2_1_2:
        return <Indicator_2_1_2_Response />;
      case Indicator_2_1_3:
        // Show both Part A and Part B for all strategies (Elimination, Burden Reduction, etc.)
        return <Indicator_2_1_3_Response />;
      case Indicator_2_1_4:
        if (strategyId === StrategiesEnum.BurdenReduction.toLowerCase())
          return <Indicator_2_1_4_Burden_Response />;
        else if (strategyId === StrategiesEnum.Both.toLowerCase())
          return <Indicator_2_1_4_Both_Response />;
        else return <Indicator_2_1_4_Response />;
      case Indicator_2_2_1:
        return <Indicator_2_2_1_Response />;
      case Indicator_2_2_2:
        return <Indicator_2_2_2_ResponseContainer />;
      case Indicator_2_2_3:
        return <Indicator_2_2_3_Response />;
      case Indicator_2_2_4:
        return <Indicator_2_2_4_Response />;
      case Indicator_2_2_5:
        return <Indicator_2_2_5_Response />;
      case Indicator_2_2_6:
        return <Indicator_2_2_6_Response />;
      case Indicator_2_3_1:
        return <Indicator_2_3_1_Response />;
      case Indicator_2_3_2:
        return <Indicator_2_3_2_Response />;
      case Indicator_2_4_1:
        return <Indicator_2_4_1_Response />;
      case Indicator_2_4_2:
        return <Indicator_2_4_2_Response />;
      case Indicator_2_4_4:
        return <Indicator_2_4_4_Response />;
      case Indicator_2_5_1:
        return <Indicator_2_5_1_Response />;

      // Objective 3
      case Indicator_3_1_2:
        if (strategyId === StrategiesEnum.BurdenReduction.toLowerCase())
          return <Indicator_3_1_2_Burden_ResponseContainer />;
        else return <Indicator_3_1_2_ResponseContainer />;
      case Indicator_3_1_3:
        return <Indicator_3_1_3_Response />;
      case Indicator_3_2_1:
        return <Indicator_3_2_1_Response />;
      case Indicator_3_2_2:
        if (strategyId === StrategiesEnum.BurdenReduction.toLowerCase())
          return <Indicator_3_2_2_Burden_ResponseContainer />;
        else if (strategyId === StrategiesEnum.Both.toLowerCase())
          return <Indicator_3_2_2_Both_ResponseContainer />;
        else return <Indicator_3_2_2_ResponseContainer />;
      case Indicator_3_2_3:
        return <Indicator_3_2_3_Response />;
      case Indicator_3_3_1:
        return <Indicator_3_3_1_Response />;
      case Indicator_3_3_2:
        return <Indicator_3_3_2_Response />;
      case Indicator_3_3_4:
        return <Indicator_3_3_4_Response />;
      case Indicator_3_4_2:
        return <Indicator_3_4_2_Response />;
      case Indicator_3_3_3:
        return <Indicator_3_3_3_Response />;
      case Indicator_3_4_1:
        return <Indicator_3_4_1_Response />;
      case Indicator_3_5_1:
        return <Indicator_3_5_1_Response />;
      case Indicator_3_5_2:
        return <Indicator_3_5_2_Response />;
      case Indicator_3_5_3:
        return <Indicator_3_5_3_Response />;
      case Indicator_3_6_1:
        return <Indicator_3_6_1_Response />;

      // Objective 4
      case Indicator_4_1_1:
        return <Indicator_4_1_1_Response />;
      case Indicator_4_1_2:
        return <Indicator_4_1_2_Response />;
      case Indicator_4_1_3:
        return <Indicator_4_1_3_Response />;
      case Indicator_4_2_1:
        return <Indicator_4_2_1_Response />;
      case Indicator_4_2_2:
        return <Indicator_4_2_2_Response />;
      case Indicator_4_3_1:
        return <Indicator_4_3_1_Response />;
      case Indicator_4_4_1:
        if (strategyId === StrategiesEnum.BurdenReduction.toLowerCase())
          return <Indicator_4_4_1_Burden_Response />;
        else return <Indicator_4_4_1_Response />;
      case Indicator_4_4_2:
        return <Indicator_4_4_2_Response />;
      case Indicator_4_4_3:
        return <Indicator_4_4_3_Response />;

      default:
        return (
          <UserMessage
            renderContent={
              <h5>
                Response for indicator{' '}
                <strong>{`${sequence} ${label}`} </strong> is under development.
              </h5>
            }
          />
        );
    }
  };

  return { renderResponse };
}

export default useCaseStrategies;
