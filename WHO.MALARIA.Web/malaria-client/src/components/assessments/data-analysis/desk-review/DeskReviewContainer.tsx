﻿import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import { TabModel } from "../../../../models/TabModel";
import WHOTabs from "../../../controls/WHOTabs";
import Tabs, { WHOTab } from "../../../controls/Tabs";
import DataGrid from "../../../controls/DataGrid";
import {
  GridCellProps,
  GridColumnProps,
  GridHeaderCellProps,
} from "@progress/kendo-react-grid";
import { Button, IconButton } from "@mui/material";
import { analyticalOutputService } from "../../../../services/analyticalOutputService";
import {
  AnalyticalOutputDetailsModel,
  IndicatorModel,
  ObjectiveModel,
  SubObjectiveModel,
  StrategyModel,
} from "../../../../models/DataAnalysis/DeskReview/AnalyticalOutputDetailsModel";
import Checkbox from "../../../controls/Checkbox";
import { StrategiesEnum } from "../../../../models/Enums";
import { Constants } from "../../../../models/Constants";
import {
  IndicatorSelectionRequestModel,
  IndicatorsRequestModel,
} from "../../../../models/RequestModels/AnalyticalOutputRequestModel";
import { UtilityHelper } from "../../../../utils/UtilityHelper";
import DownloadInProgressModal from "../../../controls/DownloadInProgressModal";

/** Renders the desk review container for data analysis and all components underneath */
const DeskReviewContainer = () => {
  const { t } = useTranslation();
  const location: any = useLocation();
  const navigate = useNavigate();
  const assessmentId = location?.state?.assessmentId;

  const [currentStrategyTabIndex, setCurrentStrategyTabIndex] =
    useState<number>(0);
  const [currentObjectiveTabIndex, setCurrentObjectiveTabIndex] =
    useState<number>(0);
  const [currentSubObjectiveTabIndex, setCurrentSubObjectiveTabIndex] =
    useState<number>(0);

  const [subObjectiveId, setSubObjectiveId] = useState("");
  const [objectiveId, setObjectiveId] = useState("");
  const [strategyId, setStrategyId] = useState<string>("");

  const [assessmentStrategies, setAssessmentStrategies] = useState<
    Array<TabModel>
  >([]);
  const [objectives, setObjectives] = useState<Array<ObjectiveModel>>([]);
  const [filteredObjectives, setFilteredObjectives] = useState<
    Array<ObjectiveModel>
  >([]);
  const [subObjectives, setSubObjectives] = useState<Array<SubObjectiveModel>>(
    []
  );
  const [filteredSubObjectives, setFilteredSubObjectives] = useState<
    Array<SubObjectiveModel>
  >([]);
  const [indicators, setIndicators] = useState<Array<IndicatorModel>>([]);
  const [filteredIndicators, setFilteredIndicators] = useState<
    Array<IndicatorModel>
  >([]);
  const [analyticalOutputIndicators, setAnalyticalOutputIndicators] = useState<
    Array<IndicatorSelectionRequestModel>
  >([]);

  const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);

  const showObjective_3_UploadDiagramButton: boolean =
    objectiveId === Constants.Common.TechnicalAndProcessObjectiveId;
  const showSubObjective_2_2_UploadDiagramButton: boolean =
    subObjectiveId === Constants.Common.InformationSystemSubObjectiveId;
  const currentlanguage = UtilityHelper.getCookieValue("i18next");

  useEffect(() => {
    bindStrategiesObjectivesSubobjectivesIndicators();
  }, [currentlanguage]);

  // check if user can be navigated back to where he has left
  const canNavigateBack = () =>
    location?.state?.selectedStrategyId &&
    location?.state?.selectedObjectiveId &&
    location?.state?.selectedSubObjectiveId;

  // render checkbox as first column
  const renderCheckBox = (props: GridCellProps) => {
    return (
      <td>
        <Checkbox
          color="primary"
          className="p-0"
          label=""
          name="indicator"
          value={props.dataItem["id"]}
          disableRipple
          disableFocusRipple
          disableTouchRipple
          inputProps={{ "aria-label": "secondary checkbox" }}
          checked={props.dataItem["isSelected"]}
          onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
            onIndicatorSelection(evt, props.dataItem["id"]);
          }}
        />
      </td>
    );
  };

  // Triggers whenever indicators are selected
  const onIndicatorSelection = (
    evt: React.ChangeEvent<HTMLInputElement>,
    indicatorId: string
  ) => {
    let selectedIndicators: Array<IndicatorSelectionRequestModel> = [];

    if (evt.target.checked) {
      const indicator: IndicatorModel | undefined = filteredIndicators.find(
        (indicator: IndicatorModel) => indicator?.id == indicatorId
      );

      if (indicator) {
        const selectedIndicator: IndicatorSelectionRequestModel =
          new IndicatorSelectionRequestModel(
            indicator.id,
            indicator.assessmentIndicatorId,
            indicator.assessmentStrategyId,
            evt.target.checked
          );

        selectedIndicators = [...analyticalOutputIndicators, selectedIndicator];
      }
    } else {
      selectedIndicators = analyticalOutputIndicators.filter(
        (indicator: IndicatorSelectionRequestModel) =>
          indicator.indicatorId !== indicatorId
      );
    }

    const indicatorsData = filteredIndicators.map(
      (indicator: IndicatorModel) => {
        let isSelected: boolean = false;
        if (
          selectedIndicators.some(
            (ind: IndicatorSelectionRequestModel) =>
              ind.indicatorId == indicator.id
          )
        ) {
          isSelected = true;
        }

        return new IndicatorModel(
          indicator.strategyId,
          indicator.id,
          indicator.name,
          indicator.sequence,
          indicator.subObjectiveId,
          indicator.assessmentIndicatorId,
          indicator.assessmentStrategyId,
          isSelected
        );
      }
    );

    setFilteredIndicators(indicatorsData);
    setAnalyticalOutputIndicators(selectedIndicators);
  };

  //Triggers when clicked on the select all indicators for the sub-objectives
  const onAllIndicatorSelection = (
    evt: React.ChangeEvent<HTMLInputElement>
  ) => {
    let selectedIndicators: Array<IndicatorSelectionRequestModel> = [];

    const indicatorsData = filteredIndicators.map(
      (indicator: IndicatorModel) => {
        if (evt.target.checked) {
          if (
            !selectedIndicators.some(
              (ind: IndicatorSelectionRequestModel) =>
                ind.indicatorId === indicator.id
            )
          ) {
            selectedIndicators = [
              ...selectedIndicators,
              new IndicatorSelectionRequestModel(
                indicator.id,
                indicator.assessmentIndicatorId,
                indicator.assessmentStrategyId,
                evt.target.checked
              ),
            ];
          }
        }

        return new IndicatorModel(
          indicator.strategyId,
          indicator.id,
          indicator.name,
          indicator.sequence,
          indicator.subObjectiveId,
          indicator.assessmentIndicatorId,
          indicator.assessmentStrategyId,
          evt.target.checked
        );
      }
    );

    setFilteredIndicators(indicatorsData);
    setAnalyticalOutputIndicators(selectedIndicators);
  };

  // Checks if all indicators are selected under the selected sub-objectives
  const areAllIndicatorsSelected = filteredIndicators.every(
    (a) => a.isSelected === true
  );

  // Checks if any of the indicator is selected from the indicator list and based on this export button is shown on the grid
  const hasAnyIndicatorSelected = filteredIndicators.some(
    (ind: IndicatorModel) => ind.isSelected === true
  );

  //Create column definition
  const getColDefs = (): Array<GridColumnProps> => {
    return [
      {
        width: "120px",
        field: "SELECTED_FIELD",
        cells: {data:(props: GridCellProps) => renderCheckBox(props),
        headerCell: (props: GridHeaderCellProps) => (
          <span>
            <Checkbox
              className="p-0"
              label=""
              name="selectAll"
              checked={areAllIndicatorsSelected}
              onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                onAllIndicatorSelection(evt);
              }}
            />
          </span>
        )},
      },

      {
        title: "Indicator",
        sortable: true,
        cells: {data:(props: GridCellProps) => (
          <td>
            <span>{props.dataItem["sequence"]}</span>
            <span className="ps-2">{props.dataItem["name"]} </span>
          </td>
        )},
      },
      {
        sortable: true,
        cells: {data:(props: GridCellProps) => (
          <td>
            <IconButton className="grid-icon-button action-btn">
              <Button
                className="btn app-btn-secondary"
                onClick={() =>
                  onViewClick(
                    props.dataItem["id"],
                    props.dataItem["name"],
                    props.dataItem["sequence"],
                    props.dataItem["assessmentIndicatorId"],
                    props.dataItem["assessmentStrategyId"]
                  )
                }
              >
                {t("Common.View")}
              </Button>
            </IconButton>
          </td>
        )},
      },
    ];
  };

  //Triggers whenever user takes an action to view the analytical output indicators
  const onViewClick = (
    id: string,
    name: string,
    sequence: string,
    assessmentIndicatorId: string,
    assessmentStrategyId: string
  ) => {
    const subObjective = subObjectives.find(
      (subObjective: SubObjectiveModel) => subObjective.id === subObjectiveId
    );
    const subObjectiveName = `${subObjective?.sequence} ${subObjective?.name}`;

    navigate("/assessment/data-analysis/desk-review/indicator/view", {
      state: {
        indicatorId: id,
        assessmentId: assessmentId,
        indicatorName: name,
        sequence,
        status: location.state.status,
        assessmentIndicatorId,
        assessmentStrategyId,
        subObjectiveName,
        selectedStrategyId: strategyId,
        // used to land user on same objective and sub-objective section(screen)
        selectedObjectiveId: objectiveId,
        selectedSubObjectiveId: subObjectiveId,
        selectedStrategyTabIndex: currentStrategyTabIndex,
        selectedObjectiveTabIndex: currentObjectiveTabIndex,
        selectedSubObjectiveTabIndex: currentSubObjectiveTabIndex,
        approach: location.state.approach,
      },
    });
    return;
  };

  //Bind strategies, objectives, sub-objectives and indicators based on assessmentId
  const bindStrategiesObjectivesSubobjectivesIndicators = () => {
    analyticalOutputService
      .getStrategiesObjectivesSubobjectivesIndicators(assessmentId)
      .then((response: AnalyticalOutputDetailsModel) => {
        const options = response.strategies.map(
          (strategy: StrategyModel): TabModel =>
            new TabModel(strategy.id, strategy.name, <></>)
        );

        options.forEach(function (val: TabModel) {
          val.label = t(`Common.${val.label}`);
        });

        setAssessmentStrategies(options);
        setObjectives(response.objectives);
        setSubObjectives(response.subObjectives);
        setIndicators(response.indicators);

        if (canNavigateBack()) {
          onNavigateBack(
            response.objectives,
            response.subObjectives,
            response.indicators
          );
        } else {
          setStrategyId(response.strategies[0]?.id);

          filterObjective(
            response.objectives,
            response.subObjectives,
            response.indicators,
            response.strategies[0]?.id
          );
        }
      });
  };

  // Helps user to redirect back to the same screen
  const onNavigateBack = (
    objectives: Array<ObjectiveModel>,
    subObjectives: Array<SubObjectiveModel>,
    indicators: Array<IndicatorModel>
  ) => {
    const strategyId = location.state.selectedStrategyId;
    const objectiveId = location.state.selectedObjectiveId;
    const subObjectiveId = location.state.selectedSubObjectiveId;

    setStrategyId(strategyId);
    setObjectiveId(objectiveId);
    setSubObjectiveId(subObjectiveId);

    setCurrentStrategyTabIndex(location.state.selectedStrategyTabIndex);
    setCurrentObjectiveTabIndex(location.state.selectedObjectiveTabIndex);
    setCurrentSubObjectiveTabIndex(location.state.selectedSubObjectiveTabIndex);

    const filteredObjectives: Array<ObjectiveModel> = objectives.filter(
      (objective: ObjectiveModel) => objective.strategyId === strategyId
    );

    setFilteredObjectives(filteredObjectives);

    const filteredSubObjectives: Array<SubObjectiveModel> =
      subObjectives.filter(
        (subObjective: SubObjectiveModel) =>
          subObjective.objectiveId === objectiveId &&
          subObjective.strategyId === strategyId
      );

    setFilteredSubObjectives(filteredSubObjectives);

    if (filteredSubObjectives.length) {
      const filteredIndicators: Array<IndicatorModel> = indicators.filter(
        (indicator: IndicatorModel) =>
          indicator.subObjectiveId === subObjectiveId &&
          indicator.strategyId === strategyId
      );

      setFilteredIndicators(filteredIndicators);
    }
  };

  //Renders the objectives tabs
  const renderObjectivesTab = filteredObjectives.map(
    (objective: ObjectiveModel): WHOTab => {
      return { id: objective.id, label: objective.name };
    }
  );

  //Renders the sub-objectives tabs
  const renderSubObjectivesTab = filteredSubObjectives.map(
    (subObjective: SubObjectiveModel): WHOTab => {
      return {
        id: subObjective.id,
        label: `${subObjective.sequence} ${subObjective.name}`,
      };
    }
  );

  //Get the filtered objectives by strategyId
  const filterObjective = (
    objectives: Array<ObjectiveModel>,
    subObjectives: Array<SubObjectiveModel>,
    indicators: Array<IndicatorModel>,
    strategyId: string
  ) => {
    const filteredObjectives = objectives.filter(
      (objective: ObjectiveModel) => objective.strategyId === strategyId
    );

    setFilteredObjectives(filteredObjectives);
    setObjectiveId(filteredObjectives[0]?.id);
    setCurrentObjectiveTabIndex(0);
    filterSubObjective(
      filteredObjectives[0]?.id,
      subObjectives,
      indicators,
      strategyId
    );
    setAnalyticalOutputIndicators([]);
  };

  //Get the filtered sub-objective by objectives
  const filterSubObjective = (
    objectiveId: string,
    subObjectives: Array<SubObjectiveModel>,
    indicators: Array<IndicatorModel>,
    strategyId: string
  ) => {
    const filteredSubObjectives = subObjectives.filter(
      (subObjective: SubObjectiveModel) =>
        subObjective.objectiveId === objectiveId &&
        subObjective.strategyId === strategyId
    );

    setFilteredSubObjectives(filteredSubObjectives);

    if (filteredSubObjectives.length) {
      filterIndicators(filteredSubObjectives[0].id, indicators, strategyId);
      setSubObjectiveId(filteredSubObjectives[0].id);
    }

    setCurrentSubObjectiveTabIndex(0);
  };

  //Get the filtered indicators by sub-objectives
  const filterIndicators = (
    subObjectiveId: string,
    indicators: Array<IndicatorModel>,
    strategyId: string
  ) => {
    const filteredIndicators = indicators.filter(
      (indicator: IndicatorModel) =>
        indicator.subObjectiveId === subObjectiveId &&
        indicator.strategyId === strategyId
    );

    setFilteredIndicators(filteredIndicators);
  };

  //Triggers whenever user changes the objective tab
  const onObjectiveTabChange = (currentTabIndex: number) => {
    setObjectiveId(filteredObjectives[currentTabIndex].id);
    setCurrentObjectiveTabIndex(currentTabIndex);

    filterSubObjective(
      filteredObjectives[currentTabIndex].id,
      subObjectives,
      indicators,
      strategyId
    );
  };

  //Triggers whenever user changes the sub-objective tab
  const onSubObjectiveTabChange = (currentSubObjectiveIndex: number) => {
    const _subObjectivesId = filteredSubObjectives[currentSubObjectiveIndex].id;

    setCurrentSubObjectiveTabIndex(currentSubObjectiveIndex);
    setSubObjectiveId(_subObjectivesId);

    filterIndicators(
      filteredSubObjectives[currentSubObjectiveIndex].id,
      indicators,
      strategyId
    );
  };

  //Triggers whenever user changes the strategy dropdown
  const onStrategyTabChange = (event: React.ChangeEvent<{}>, newValue: any) => {
    setCurrentStrategyTabIndex(newValue);
    const strategyId = assessmentStrategies[newValue].id as string;
    setStrategyId(strategyId);

    filterObjective(objectives, subObjectives, indicators, strategyId);
  };

  // Triggers whenever user clicks on generate report button
  const onGenerateReport = () => {
    setIsFileDownloading(true);

    analyticalOutputService
      .downloadReport(
        new IndicatorsRequestModel(
          assessmentId,
          strategyId,
          analyticalOutputIndicators
        )
      )
      .then((response: any) => {
        UtilityHelper.download(response, "AnalyticalOutput.xlsx");
        setIsFileDownloading(false);
      })
      .catch((error: any) => {
        setIsFileDownloading(false);
      });
  };

  // Triggers whenever user clicks on generate report button
  const onGenerateReportForAll = () => {
    setIsFileDownloading(true);

    analyticalOutputService
      .downloadReportForAll(
        new IndicatorsRequestModel(assessmentId, strategyId, [])
      )
      .then((response: any) => {
        UtilityHelper.download(response, "AnalyticalOutput.xlsx");
        setIsFileDownloading(false);
      })
      .catch((error: any) => {
        setIsFileDownloading(false);
      });
  };

  // Triggers whenever user takes an action to view uploaded diagram for (sub-objective 2_2/objective 3) for Desk Review
  const onUploadedDiagramClick = (diagramFor: string) => {
    const state = location?.state;
    navigate(Constants.Route.Url.ANALYTICAL_OUTPUT_UPLOADED_DIAGRAM, {
      state: {
        ...state,
        diagramFor,
        selectedStrategyId: strategyId,
        // used to land user on same objective and sub-objective section(screen)
        selectedObjectiveId: objectiveId,
        selectedSubObjectiveId: subObjectiveId,
        selectedStrategyTabIndex: currentStrategyTabIndex,
        selectedObjectiveTabIndex: currentObjectiveTabIndex,
        selectedSubObjectiveTabIndex: currentSubObjectiveTabIndex,
      },
    });
  };

  return (
    <section className="page-full-section">
      <>
        {assessmentStrategies.length ? (
          <div className="app-tab-wrapper">
            <WHOTabs
              tabs={assessmentStrategies}
              scrollable={true}
              value={currentStrategyTabIndex}
              onChange={onStrategyTabChange}
            >
              <>
                <Tabs
                  tabs={renderObjectivesTab}
                  activeTabIndex={currentObjectiveTabIndex}
                  onClick={onObjectiveTabChange}
                >
                  <div className="category-wrapper mt-3">
                    <div className="d-flex align-items-stretch">
                      <div className="subcategories-wrapper">
                        <Tabs
                          tabs={renderSubObjectivesTab}
                          activeTabIndex={currentSubObjectiveTabIndex}
                          onClick={onSubObjectiveTabChange}
                        >
                          <></>
                        </Tabs>
                      </div>

                      <div className="indicators-wrapper">
                        <div className="d-flex justify-content-end align-items-center mb-2">
                          <div className="button-action-wrapper">
                            <div className="button-action-section d-flex align-items-center">
                              {showSubObjective_2_2_UploadDiagramButton &&
                                (strategyId ===
                                  StrategiesEnum.BurdenReduction.toLowerCase() ||
                                  strategyId ===
                                    StrategiesEnum.Elimination.toLowerCase() ||
                                  strategyId ===
                                    StrategiesEnum.Both.toLowerCase()) && (
                                  <Button
                                    size="small"
                                    className="btn app-btn-secondary"
                                    onClick={() =>
                                      onUploadedDiagramClick(
                                        Constants.Diagrams.SubObjective
                                      )
                                    }
                                  >
                                    {t("Assessment.DataAnalysis.ViewDiagram")}
                                  </Button>
                                )}
                              {showObjective_3_UploadDiagramButton &&
                                (strategyId ===
                                  StrategiesEnum.BurdenReduction.toLowerCase() ||
                                  strategyId ===
                                    StrategiesEnum.Elimination.toLowerCase() ||
                                  strategyId ===
                                    StrategiesEnum.Both.toLowerCase()) && (
                                  <Button
                                    size="small"
                                    className="btn app-btn-secondary"
                                    onClick={() =>
                                      onUploadedDiagramClick(
                                        Constants.Diagrams.Objective
                                      )
                                    }
                                  >
                                    {t("Assessment.DataAnalysis.ViewDiagram")}
                                  </Button>
                                )}
                              {hasAnyIndicatorSelected && (
                                <Button
                                  size="small"
                                  className="btn app-btn-secondary"
                                  onClick={onGenerateReport}
                                >
                                  {t("Common.Export")}
                                </Button>
                              )}
                              {filteredIndicators.length > 0 && (
                                <Button
                                  size="small"
                                  className="btn app-btn-secondary"
                                  onClick={onGenerateReportForAll}
                                >
                                  {t("Common.ExportAll")}
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>

                        <DataGrid
                          className="k-grid-wrapper k-grid-action-wrapper k-desk-review hide-grid-icon"
                          columns={getColDefs()}
                          data={filteredIndicators}
                          selectable={{
                            enabled: true,
                            drag: false,
                            cell: true,
                            mode: "single",
                          }}
                          hasActionBtn={true}
                        />
                      </div>
                    </div>
                  </div>
                </Tabs>
              </>
            </WHOTabs>
          </div>
        ) : (
          <div className="d-flex h-400 justify-content-center align-items-center">
            {t("Assessment.DataAnalysis.CompleteAssessmentForDataAnalysis")}
          </div>
        )}
      </>

      <DownloadInProgressModal isFileDownloading={isFileDownloading} />
    </section>
  );
};

export default DeskReviewContainer;
