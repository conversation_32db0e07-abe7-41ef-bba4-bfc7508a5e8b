export class Response_1 {
  constructor(
    public cannotBeAssessed: boolean | false,
    public cannotBeAssessedReason: string | null,
    public metNotMetStatus: string | null,
    public transmitMalariaIndicators: Array<TransmitMalariaIndicator>,
    // property is used for checklist indicators count and compare it with malariaIndicators arrayOfObject count in validation rules
    public checkListIndicatorsCount: number,
    public hasPVivaxCases: boolean | null = null
  ) {
    this.cannotBeAssessed = cannotBeAssessed;
    this.cannotBeAssessedReason = cannotBeAssessedReason;
    this.metNotMetStatus = metNotMetStatus;
    this.transmitMalariaIndicators = transmitMalariaIndicators;
    this.hasPVivaxCases = hasPVivaxCases;
  }
  static init = () => new Response_1(false, null, null, [], 0, null);
}

export class TransmitMalariaIndicator {
  constructor(
    public checklistIndicatorId: string,
    public indicatorMonitored: boolean | null = null,
    public underFive: boolean | null = null,
    public overFive: boolean | null = null,
    public gender: boolean | null = null,
    public pregnantWoman: boolean | null = null,
    public healthSector: boolean | null = null,
    public geography: boolean | null = null,
    public confirmationMethod: boolean | null = null,
    public other: boolean | null = null
  ) {
    this.checklistIndicatorId = checklistIndicatorId;
    this.indicatorMonitored = indicatorMonitored;
    this.underFive = underFive;
    this.overFive = overFive;
    this.gender = gender;
    this.pregnantWoman = pregnantWoman;
    this.healthSector = healthSector;
    this.geography = geography;
    this.confirmationMethod = confirmationMethod;
    this.other = other;
  }
  [index: string]: string | any;
}
