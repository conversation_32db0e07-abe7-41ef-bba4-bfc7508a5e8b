﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>7fca10dd-9065-4e9f-bf4f-cd2556f6c90e</UserSecretsId>
    <!-- Linux deployment optimizations -->
    <RuntimeIdentifiers>win-x64;linux-x64;osx-x64</RuntimeIdentifiers>
    <PublishReadyToRun>false</PublishReadyToRun>
    <PublishSingleFile>false</PublishSingleFile>
  </PropertyGroup>
  <PropertyGroup>
    <SpaRoot>malaria-client/</SpaRoot>
    <DefaultItemExcludes>$(DefaultItemExcludes);$(SpaRoot)node_modules/**</DefaultItemExcludes>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <!-- Linux production optimizations -->
    <DebugType>None</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <Optimize>true</Optimize>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Templates\NewFolder\**" />
    <Content Remove="Templates\NewFolder\**" />
    <EmbeddedResource Remove="Templates\NewFolder\**" />
    <None Remove="Templates\NewFolder\**" />
    <TypeScriptCompile Remove="Templates\NewFolder\**" />
  </ItemGroup>
  <ItemGroup>
    <Content Remove="Areas\idp\Views\Account\Login2.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Assets\en\Introduction to the Malaria Surveillance Assessment Toolkit.pdf" />
    <None Remove="Assets\en\MalariaSurveillanceToolkitTools.zip" />
    <None Remove="Assets\fr\Introduction to the Malaria Surveillance Assessment Toolkit.pdf" />
    <None Remove="Assets\fr\MalariaSurveillanceToolkitTools.zip" />
    <None Remove="Templates\AnalyticalOutput\AnalyticalOutputTemplate.xlsx" />
    <None Remove="Templates\DQA\DQA_desk_level_tool_report_EN.xlsx" />
    <None Remove="Templates\DQA\DQA_desk_level_tool_report_FR.xlsx" />
    <None Remove="Templates\DQA\DQA_Elimination.xlsx" />
    <None Remove="Templates\DQA\DQA_Elimination_FR.xlsx" />
    <None Remove="Templates\QuestionBank\Health Facility Template_EN.xlsx" />
    <None Remove="Templates\QuestionBank\Health Facility Template_FR.xlsx" />
    <None Remove="Templates\QuestionBank\QuestionBankTemplate.xlsx" />
    <None Remove="Templates\ShellTable\ShellTableDataTemplate.xlsx" />
    <None Remove="Templates\ShellTable\ShellTableTemplate.xlsx" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Assets\en\Introduction to the Malaria Surveillance Assessment Toolkit.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Assets\en\MalariaSurveillanceToolkitTools.zip">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Assets\fr\Introduction to the Malaria Surveillance Assessment Toolkit.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Assets\fr\MalariaSurveillanceToolkitTools.zip">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\AnalyticalOutput\AnalyticalOutputTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
	<Content Include="Templates\AnalyticalOutput\AnalyticalOutputTemplate_EN.xlsx">
		<CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
	<Content Include="Templates\AnalyticalOutput\AnalyticalOutputTemplate_FR.xlsx">
		<CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
	<Content Include="Templates\DQA\DQA_desk_level_tool_report_EN.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
	<Content Include="Templates\DQA\DQA_desk_level_tool_report_FR.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
	<Content Include="Templates\DQA\DQA_Elimination.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
	<Content Include="Templates\DQA\DQA_Elimination_FR.xlsx">
	  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
    <Content Include="Templates\QuestionBank\Health Facility Template_EN.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\QuestionBank\Health Facility Template_FR.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\QuestionBank\QuestionBankTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
	<Content Include="Templates\QuestionBank\QuestionBankTemplate_EN.xlsx">
		<CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
	<Content Include="Templates\QuestionBank\QuestionBankTemplate_FR.xlsx">
		<CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
    <Content Include="Templates\ShellTable\ShellTableDataTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Templates\ShellTable\ShellTableTemplate.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
	<Content Include="Templates\ShellTable\ShellTableTemplate_EN.xlsx">
		<CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
	<Content Include="Templates\ShellTable\ShellTableTemplate_FR.xlsx">
		<CopyToOutputDirectory>Always</CopyToOutputDirectory>
	</Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Autofac" Version="7.1.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Cloudmersive.APIClient.NETCore.VirusScan" Version="2.1.1" />
    <PackageReference Include="Duende.IdentityServer" Version="6.3.6" />
    <PackageReference Include="Duende.IdentityServer.AspNetIdentity" Version="6.3.6" />
    <PackageReference Include="MediatR" Version="12.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Facebook" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Graph" Version="5.36.0" />
    <PackageReference Include="Microsoft.Graph.Core" Version="3.1.2" />
    <!-- Temporarily disabled TypeScript MSBuild
    <PackageReference Include="Microsoft.TypeScript.MSBuild" Version="5.2.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    -->
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.0" />
    <PackageReference Include="Opw.HttpExceptions.AspNetCore" Version="4.0.2" />
    <PackageReference Include="SendGrid.Extensions.DependencyInjection" Version="1.0.1" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="6.3.0" />
    <PackageReference Include="Serilog.Sinks.PeriodicBatching" Version="3.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.5.0" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WHO.MALARIA.Common\WHO.MALARIA.Common.csproj" />
    <ProjectReference Include="..\WHO.MALARIA.Database\WHO.MALARIA.Database.csproj" />
    <ProjectReference Include="..\WHO.MALARIA.Domain\WHO.MALARIA.Domain.csproj" />
    <ProjectReference Include="..\WHO.MALARIA.Services\WHO.MALARIA.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="tempkey.jwk">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>

  <ItemGroup>
    <None Update="start-linux.sh">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <None Update="Assets\Download\AllTool.zip">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Assets\en\AllTools.zip">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Assets\fr\AllTools.zip">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Templates\DQA\DeskLevelDQA_introduction_tab_EN.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Templates\DQA\DeskLevelDQA_introduction_tab_FR.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>

    <None Update="Templates\DQA\DQA_desk_level_tool_report.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>

    <None Update="Templates\DQA\DQA_SERVICE_EN.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>

    <None Update="Templates\DQA\DQA_SERVICE_FR.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Content Update="malaria-client\public\assets\locales\en\indicator-guide.en.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="malaria-client\public\assets\locales\en\indicators-responses.en.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="malaria-client\public\assets\locales\en\translation.en.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="malaria-client\public\assets\locales\fr\indicator-guide.fr.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="malaria-client\public\assets\locales\fr\indicators-responses.fr.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="malaria-client\public\assets\locales\fr\translation.fr.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="FatalLogs\" />
    <Content Update="wwwroot\ImplementationReferenceGuide.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- Temporarily disabled TypeScript build during .NET build
  <Target Name="DebugEnsureNodeEnv" BeforeTargets="Build" Condition=" '$(Configuration)' == 'Debug' And !Exists('$(SpaRoot)node_modules') ">
    <Exec Command="node version" ContinueOnError="true">
      <Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
    </Exec>
    <Error Condition="'$(ErrorCode)' != '0'" Text="Node.js is required to build  &#xD;&#xA;        and run this project. To continue, please install Node.js from https://nodejs.org/,  &#xD;&#xA;        and then restart your command prompt or IDE." />
    <Message Importance="high" Text="Restoring dependencies using 'npm'.  &#xD;&#xA;        This may take several minutes..." />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
  </Target>
  -->

  <!-- Temporarily disabled TypeScript build during .NET build
  <Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish">
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm install" />
    <Exec WorkingDirectory="$(SpaRoot)" Command="npm run build" />

    <ItemGroup>
      <DistFiles Include="$(SpaRoot)build\**" />
      <ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
        <RelativePath>%(DistFiles.Identity)</RelativePath>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      </ResolvedFileToPublish>
    </ItemGroup>
  </Target>
  -->
</Project>
