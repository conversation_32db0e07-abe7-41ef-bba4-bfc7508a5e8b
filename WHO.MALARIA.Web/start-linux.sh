#!/bin/bash

# WHO Malaria Surveillance Tool - Linux Startup Script
# This script helps start the application on Linux systems

set -e

# Configuration
APP_NAME="WHO.MALARIA.Web"
APP_DIR="$(dirname "$0")"
LOG_DIR="$APP_DIR/logs"
PID_FILE="$APP_DIR/$APP_NAME.pid"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create logs directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Check if binary exists and is executable
if [ ! -f "$APP_DIR/$APP_NAME" ]; then
    log_error "Application binary '$APP_NAME' not found in $APP_DIR"
    exit 1
fi

if [ ! -x "$APP_DIR/$APP_NAME" ]; then
    log_warn "Setting executable permissions for $APP_NAME"
    chmod +x "$APP_DIR/$APP_NAME"
fi

# Function to start the application
start_app() {
    log_info "Starting WHO Malaria Surveillance Tool..."
    
    # Set environment variables for production
    export ASPNETCORE_ENVIRONMENT="${ASPNETCORE_ENVIRONMENT:-Production}"
    export ASPNETCORE_URLS="${ASPNETCORE_URLS:-http://0.0.0.0:5000;https://0.0.0.0:5001}"
    export DOTNET_PRINT_TELEMETRY_MESSAGE=false
    
    log_info "Environment: $ASPNETCORE_ENVIRONMENT"
    log_info "URLs: $ASPNETCORE_URLS"
    
    # Start the application
    cd "$APP_DIR"
    nohup "./$APP_NAME" > "$LOG_DIR/app.log" 2>&1 &
    
    # Save PID
    echo $! > "$PID_FILE"
    
    log_info "Application started with PID: $(cat $PID_FILE)"
    log_info "Logs are being written to: $LOG_DIR/app.log"
    log_info "Use 'tail -f $LOG_DIR/app.log' to monitor the application"
}

# Function to stop the application
stop_app() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        log_info "Stopping application with PID: $PID"
        kill "$PID" 2>/dev/null || log_warn "Process $PID not found or already stopped"
        rm -f "$PID_FILE"
        log_info "Application stopped"
    else
        log_warn "PID file not found. Application may not be running."
    fi
}

# Function to check application status
status_app() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_info "Application is running with PID: $PID"
            return 0
        else
            log_warn "PID file exists but process is not running"
            rm -f "$PID_FILE"
            return 1
        fi
    else
        log_info "Application is not running"
        return 1
    fi
}

# Main script logic
case "${1:-start}" in
    start)
        if status_app; then
            log_warn "Application is already running"
        else
            start_app
        fi
        ;;
    stop)
        stop_app
        ;;
    restart)
        stop_app
        sleep 2
        start_app
        ;;
    status)
        status_app
        ;;
    logs)
        if [ -f "$LOG_DIR/app.log" ]; then
            tail -f "$LOG_DIR/app.log"
        else
            log_error "Log file not found: $LOG_DIR/app.log"
            exit 1
        fi
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the application"
        echo "  stop    - Stop the application"
        echo "  restart - Restart the application"
        echo "  status  - Check application status"
        echo "  logs    - Follow application logs"
        echo ""
        echo "Environment Variables:"
        echo "  ASPNETCORE_ENVIRONMENT - Set to Development, Staging, or Production (default: Production)"
        echo "  ASPNETCORE_URLS        - Set listening URLs (default: http://0.0.0.0:5000;https://0.0.0.0:5001)"
        exit 1
        ;;
esac
