trigger:
  branches:
    include:
      - contract

pool:
  vmImage: "ubuntu-latest"

variables:
  buildConfiguration: "Release"
  apiProject: "WHO.MALARIA.Web/WHO.MALARIA.Web.csproj"
  reactAppDir: "WHO.MALARIA.Web/malaria-client"

steps:
  # Install .NET 8 SDK
  - task: UseDotNet@2
    displayName: "Install .NET 8 SDK"
    inputs:
      packageType: "sdk"
      version: "8.x"
      includePreviewVersions: false

  # Restore .NET dependencies
  - task: DotNetCoreCLI@2
    displayName: "Restore .NET packages"
    inputs:
      command: "restore"
      projects: "$(apiProject)"

  # Build .NET Web API
  - task: DotNetCoreCLI@2
    displayName: "Build .NET Web API"
    inputs:
      command: "build"
      projects: "$(apiProject)"
      arguments: "--configuration $(buildConfiguration)"

    # Publish .NET Web API
  - task: DotNetCoreCLI@2
    displayName: "Publish Web API"
    inputs:
      command: "publish"
      projects: "$(apiProject)"
      arguments: "--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/api"

  # Install Node.js 22.15.1
  - task: UseNode@1
    displayName: "Install Node.js 22.15.1"
    inputs:
      version: "22.15.1"

  # Install frontend dependencies
  - task: Npm@1
    displayName: "Install frontend dependencies"
    inputs:
      workingDir: "$(reactAppDir)"
      command: "install"

  # Build frontend (React)
  - task: Npm@1
    displayName: "Build frontend"
    inputs:
      workingDir: "$(reactAppDir)"
      command: "custom"
      customCommand: "run build"

  # Copy frontend build to API wwwroot (optional step if React is served via ASP.NET static files)
  - task: CopyFiles@2
    displayName: "Copy frontend to API wwwroot"
    inputs:
      SourceFolder: "$(reactAppDir)/build"
      Contents: "**"
      TargetFolder: "$(Build.ArtifactStagingDirectory)/api/wwwroot"

  # Publish build artifacts as uncompressed files/folders
  - task: PublishPipelineArtifact@1
    displayName: "Publish Pipeline Artifact: drop"
    inputs:
      targetPath: "$(Build.ArtifactStagingDirectory)/api"
      artifactName: "drop"
      publishLocation: "pipeline"
