using System;
using System.Linq;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.SeedingMetadata;

namespace WHO.MALARIA.Database.Seeding
{
    /// <summary>
    /// Seeding data of strategies.
    /// </summary>
    public static class StrategySeeding
    {
        /// <summary>
        /// Save strategies in Strategy table
        /// </summary>
        /// <param name="malariaDbContext">instance of MalariaDbContext</param>
        public static void Seed(MalariaDbContext malariaDbContext)
        {
            DateTime currentDate = DateTime.UtcNow;

            IQueryable<Strategy> existingRecords = malariaDbContext.Strategies.AsQueryable();

            Strategy[] newRecords = new Strategy[]
            {
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.BURDEN_REDUCTION_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Burden reduction",
                        Type = (int)StrategyType.CaseSurveillance,
                        IsActive = true,
                        ShortName = "Burden reduction",
                        Order = 5,
                        Name_FR = "Réduction de la charge"
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.ELIMINATION_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Elimination",
                        Name_FR = "Élimination",
                        Type = (int)StrategyType.CaseSurveillance,
                        IsActive = true,
                        ShortName = "Elimination",
                        Order = 10
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.Both_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Both",
                        Name_FR = "Les deux",
                        Type = (int)StrategyType.CaseSurveillance,
                        IsActive = true,
                        ShortName = "Both",
                        Order = 11
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.IPTP_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Chemoprevention:Intermittent preventative treatment of malaria in pregnant women (IPTp)",
                        Name_FR = "Chimioprévention : traitement préventif intermittent du paludisme chez la femme enceinte (TPIg)",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "IPTp",
                        Order = 15
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.IPTI_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Chemoprevention:Intermittent preventative treatment of malaria in infancy (IPTi)",
                        Name_FR = "Chimioprévention : traitement préventif intermittent du paludisme chez le nourrisson (TPIn)",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "IPTi",
                        Order = 20
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.SMC_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Chemoprevention:Seasonal malaria chemoprophylaxis (SMC)",
                        Name_FR = "Chimioprévention : chimioprévention du paludisme saisonnier (CPS)",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "SMC",
                        Order = 25
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.MDA_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Chemoprevention:Mass drug administration (MDA)",
                        Name_FR = "Chimioprevention : administration de masse de médicaments (AMM)",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "MDA",
                        Order = 30
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.ITNSRC_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Vector control:Insecticide treated nets (ITNs) distributed through routine channels",
                        Name_FR = "Lutte antivectorielle : moustiquaires imprégnées d'insecticide (MII) distribuées par des voies classiques",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "ITNs-Routine",
                        Order = 35
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.ITNSMC_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Vector control:Insecticide treated nets (ITNs) distributed through mass campaigns",
                        Name_FR = "Lutte antivectorielle : moustiquaires imprégnées d'insecticide (MII) distribuées au cours de campagnes de masse",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "ITNs-mass campaign",
                        Order = 40
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.VC_IRS_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Vector control: Indoor residual spraying (IRS)",
                        Name_FR = "Lutte antivectorielle : pulvérisation d’insecticide à effet rémanent à l’intérieur des habitations (PIH)",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "IRS",
                        Order = 45
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.VC_LSM_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Vector control: Larval source management",
                        Name_FR = "Lutte antivectorielle : gestion des gîtes larvaires",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "Larval Source Management",
                        Order = 50
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.CT_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Commodity tracking",
                        Name_FR = "Suivi des produits",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "Commodity tracking",
                        Order = 55
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.ES_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Entomological surveillance",
                        Name_FR = "Surveillance entomologique",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "Entomology",
                        Order = 60
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.DES_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Drug efficacy surveillance",
                        Name_FR = "Surveillance de l'efficacité des médicaments ",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "Drug efficacy",
                        Order = 65
                    },
                    new Strategy
                    {
                        Id = StrategySeedingMetadata.GS_ID,
                        CreatedBy =  null,
                        CreatedAt = currentDate,
                        Name = "Genomic surveillance (drug resistance and pfhrp 2/3 gene deletions)",
                        Name_FR = "Surveillance génomique (résistance aux médicaments et délétions au niveau des gènes pfhrp 2/3)",
                        Type = (int)StrategyType.MalariaControl,
                        IsActive = true,
                        ShortName = "Genomics",
                        Order = 70
                    }
            };

            // 1. delete the existing record in the database if they are not in the new records
            foreach (Strategy existingRecord in existingRecords)
            {
                if (!newRecords.Any(nr => nr.Id == existingRecord.Id))
                {
                    malariaDbContext.Strategies.Remove(existingRecord);
                }
            }

            foreach (Strategy newRecord in newRecords)
            {
                Strategy existingRecord = existingRecords.SingleOrDefault(existing => existing.Id == newRecord.Id);
                if (existingRecord != null)
                {
                    // 2.
                    // if the record already exists in the database and 
                    // is modified then update it in the database
                    if (IsRecordUpdated(existingRecord, newRecord))
                    {
                        existingRecord.Name = newRecord.Name;
                        existingRecord.IsActive = newRecord.IsActive;
                        existingRecord.Order = newRecord.Order;
                        existingRecord.ShortName = newRecord.ShortName;
                        existingRecord.Type = newRecord.Type;
                        existingRecord.UpdatedBy = null;
                        existingRecord.UpdatedAt = currentDate;
                        existingRecord.Name_FR = newRecord.Name_FR;

                        malariaDbContext.Strategies.Update(existingRecord);
                    }
                }
                else
                {
                    // 3. else add the new record into the database
                    malariaDbContext.Strategies.Add(newRecord);
                }
            }
            malariaDbContext.SaveChanges();
        }

        private static bool IsRecordUpdated(Strategy existingRecord, Strategy newRecord)
        {
            return (existingRecord.Name != newRecord.Name
                || existingRecord.IsActive != newRecord.IsActive
                || existingRecord.Order != newRecord.Order
                || existingRecord.Type != newRecord.Type
                || existingRecord.ShortName != newRecord.ShortName
                || existingRecord.Name_FR != newRecord.Name_FR);
        }
    }
}
