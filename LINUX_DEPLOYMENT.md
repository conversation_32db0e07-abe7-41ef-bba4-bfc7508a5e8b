# WHO Malaria Surveillance Tool - Linux Deployment Guide

This guide provides comprehensive instructions for deploying the WHO Malaria Surveillance Tool on Linux systems.

## 🐧 Prerequisites

### System Requirements
- **Operating System**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+ / Debian 11+
- **Architecture**: x64 (64-bit)
- **Memory**: Minimum 2GB RAM, Recommended 4GB+
- **Storage**: Minimum 1GB free space
- **Network**: HTTP/HTTPS ports (80/443 or custom ports)

### Required Software
```bash
# Install .NET 8 Runtime (if not using self-contained deployment)
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt-get update
sudo apt-get install -y aspnetcore-runtime-8.0

# Install reverse proxy (optional but recommended)
sudo apt-get install -y nginx
```

## 📦 Deployment Steps

### 1. Download and Extract Artifacts
```bash
# Create application directory
sudo mkdir -p /opt/malaria-surveillance
cd /opt/malaria-surveillance

# Extract your deployment artifacts here
# (Download from Azure DevOps artifacts)
sudo tar -xzf malaria-surveillance-artifacts.tar.gz

# Set ownership
sudo chown -R $USER:$USER /opt/malaria-surveillance
```

### 2. Configure Application
```bash
# Make scripts executable (if not already done by pipeline)
chmod +x WHO.MALARIA.Web
chmod +x start-linux.sh

# Create configuration directory
mkdir -p config
mkdir -p logs
```

### 3. Environment Configuration
Create or update `appsettings.Production.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=your-db-server;Database=MalariaSurveillance;Trusted_Connection=false;User Id=your-username;Password=your-password;TrustServerCertificate=true;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:5000"
      },
      "Https": {
        "Url": "https://0.0.0.0:5001"
      }
    }
  }
}
```

### 4. Start the Application

#### Option A: Using the Startup Script (Recommended)
```bash
# Start the application
./start-linux.sh start

# Check status
./start-linux.sh status

# View logs
./start-linux.sh logs

# Stop the application
./start-linux.sh stop

# Restart the application
./start-linux.sh restart
```

#### Option B: Direct Execution
```bash
# Set environment variables
export ASPNETCORE_ENVIRONMENT=Production
export ASPNETCORE_URLS="http://0.0.0.0:5000;https://0.0.0.0:5001"

# Run the application
./WHO.MALARIA.Web
```

## 🔧 System Service Configuration (Optional)

Create a systemd service for automatic startup:

```bash
sudo nano /etc/systemd/system/malaria-surveillance.service
```

Add the following content:
```ini
[Unit]
Description=WHO Malaria Surveillance Tool
After=network.target

[Service]
Type=notify
User=www-data
Group=www-data
WorkingDirectory=/opt/malaria-surveillance
ExecStart=/opt/malaria-surveillance/WHO.MALARIA.Web
Restart=always
RestartSec=10
KillSignal=SIGINT
SyslogIdentifier=malaria-surveillance
Environment=ASPNETCORE_ENVIRONMENT=Production
Environment=ASPNETCORE_URLS=http://0.0.0.0:5000

[Install]
WantedBy=multi-user.target
```

Enable and start the service:
```bash
sudo systemctl daemon-reload
sudo systemctl enable malaria-surveillance
sudo systemctl start malaria-surveillance
sudo systemctl status malaria-surveillance
```

## 🌐 Reverse Proxy Configuration (Nginx)

Create Nginx configuration:
```bash
sudo nano /etc/nginx/sites-available/malaria-surveillance
```

Add the following content:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/malaria-surveillance /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 Security Considerations

1. **Firewall Configuration**:
```bash
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

2. **SSL Certificate** (using Let's Encrypt):
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

3. **File Permissions**:
```bash
sudo chown -R www-data:www-data /opt/malaria-surveillance
sudo chmod 755 /opt/malaria-surveillance
sudo chmod 644 /opt/malaria-surveillance/appsettings*.json
```

## 📊 Monitoring and Logs

### Application Logs
```bash
# View real-time logs
tail -f /opt/malaria-surveillance/logs/app.log

# View system service logs
sudo journalctl -u malaria-surveillance -f
```

### Health Check
```bash
# Check if application is responding
curl -I http://localhost:5000/health

# Check process
ps aux | grep WHO.MALARIA.Web
```

## 🚨 Troubleshooting

### Common Issues

1. **Permission Denied**:
```bash
chmod +x WHO.MALARIA.Web
chmod +x start-linux.sh
```

2. **Port Already in Use**:
```bash
sudo netstat -tlnp | grep :5000
sudo kill -9 <PID>
```

3. **Database Connection Issues**:
- Verify connection string in `appsettings.Production.json`
- Check database server accessibility
- Verify firewall rules

4. **Memory Issues**:
```bash
# Check memory usage
free -h
# Check application memory usage
ps aux --sort=-%mem | head
```

## 📞 Support

For deployment issues:
1. Check application logs: `./start-linux.sh logs`
2. Check system logs: `sudo journalctl -u malaria-surveillance`
3. Verify configuration files
4. Check network connectivity and firewall rules

## 🔄 Updates

To update the application:
1. Stop the current instance: `./start-linux.sh stop`
2. Backup current deployment
3. Extract new artifacts
4. Update configuration if needed
5. Start the application: `./start-linux.sh start`
