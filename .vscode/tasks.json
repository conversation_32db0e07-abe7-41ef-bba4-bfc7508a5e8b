{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/WHO.MALARIA.Web/WHO.MALARIA.Web.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary", "/property:Configuration=Debug"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/WHO.MALARIA.Web/WHO.MALARIA.Web.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/WHO.MALARIA.sln"], "problemMatcher": "$msCompile"}]}