{"version": "0.2.0", "configurations": [{"name": ".NET Core Launch (web)", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/WHO.MALARIA.Web/bin/Debug/net8.0/WHO.MALARIA.Web.dll", "args": [], "cwd": "${workspaceFolder}/WHO.MALARIA.Web", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:5001;http://localhost:5000"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "logging": {"moduleLoad": false}, "justMyCode": false, "enableStepFiltering": false, "suppressJITOptimizations": true}, {"name": "Attach to .NET Process", "type": "coreclr", "request": "attach", "processId": "${command:pickProcess}"}]}