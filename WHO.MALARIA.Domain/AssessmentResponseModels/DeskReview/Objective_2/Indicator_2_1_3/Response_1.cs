﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using WHO.MALARIA.Domain.SeedingMetadata;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_1_3
{
    /// <summary>
    /// Contains desk review response properties for Indicator 2.1.3 
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public Step_A_Response Step_A { get; set; }

        public Step_B_Response Step_B { get; set; }

        /// <summary>
        /// Validates indicator 2.1.3
        /// </summary>
        /// <returns>Validation results for indicator 2.1.3</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }

        /// <summary>
        /// Get list of elimination activities for tab a
        /// </summary>
        /// <returns>List of elimination activities</returns>
        private List<EliminationActivityReport> GetEliminationActivityReport(Delegate translator)
        {
            List<EliminationActivityReport> eliminationActivities = new List<EliminationActivityReport>();

            eliminationActivities.Add(new EliminationActivityReport(translator.DynamicInvoke(AnalyticalOutputConstants.CaseInvestigation_2_1_3).ToString(), translator.DynamicInvoke(Step_A.CaseInvestigation.ActivityInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.CaseInvestigation.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.CaseInvestigation.DataIntegratedWithRoutineCaseNotificationData.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.CaseInvestigation.DataLinkage.ConvertBoolToYesNo()).ToString(), Step_A.CaseInvestigation.DetailsOnDataLinkageToTheIndexCase, Step_A.CaseInvestigation.NationalLevelOfInvolvement, Step_A.CaseInvestigation.SubNationalLevelOfInvolvement, Step_A.CaseInvestigation.ServiceDeliveryLevelOfInvolvement, Step_A.CaseInvestigation.ChallengesWithReporting));
            eliminationActivities.Add(new EliminationActivityReport(translator.DynamicInvoke(AnalyticalOutputConstants.CaseClassification_2_1_3).ToString(), translator.DynamicInvoke(Step_A.CaseClassification.ActivityInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.CaseClassification.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.CaseClassification.DataIntegratedWithRoutineCaseNotificationData.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.CaseClassification.DataLinkage.ConvertBoolToYesNo()).ToString(), Step_A.CaseClassification.DetailsOnDataLinkageToTheIndexCase, Step_A.CaseClassification.NationalLevelOfInvolvement, Step_A.CaseClassification.SubNationalLevelOfInvolvement, Step_A.CaseClassification.ServiceDeliveryLevelOfInvolvement, Step_A.CaseClassification.ChallengesWithReporting));
            eliminationActivities.Add(new EliminationActivityReport(translator.DynamicInvoke(AnalyticalOutputConstants.FocusInvestigation_2_1_3).ToString(), translator.DynamicInvoke(Step_A.FocusInvestigation.ActivityInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.FocusInvestigation.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.FocusInvestigation.DataIntegratedWithRoutineCaseNotificationData.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.FocusInvestigation.DataLinkage.ConvertBoolToYesNo()).ToString(), Step_A.FocusInvestigation.DetailsOnDataLinkageToTheIndexCase, Step_A.FocusInvestigation.NationalLevelOfInvolvement, Step_A.FocusInvestigation.SubNationalLevelOfInvolvement, Step_A.FocusInvestigation.ServiceDeliveryLevelOfInvolvement, Step_A.FocusInvestigation.ChallengesWithReporting));
            eliminationActivities.Add(new EliminationActivityReport(translator.DynamicInvoke(AnalyticalOutputConstants.FocusClassification_2_1_3).ToString(), translator.DynamicInvoke(Step_A.FocusClassificiation.ActivityInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.FocusClassificiation.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.FocusClassificiation.DataIntegratedWithRoutineCaseNotificationData.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.FocusClassificiation.DataLinkage.ConvertBoolToYesNo()).ToString(), Step_A.FocusClassificiation.DetailsOnDataLinkageToTheIndexCase, Step_A.FocusClassificiation.NationalLevelOfInvolvement, Step_A.FocusClassificiation.SubNationalLevelOfInvolvement, Step_A.FocusClassificiation.ServiceDeliveryLevelOfInvolvement, Step_A.FocusClassificiation.ChallengesWithReporting));
            eliminationActivities.Add(new EliminationActivityReport(translator.DynamicInvoke(AnalyticalOutputConstants.ActiveCaseDetection_2_1_3).ToString(), translator.DynamicInvoke(Step_A.ActiveCaseDetection.ActivityInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.ActiveCaseDetection.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.ActiveCaseDetection.DataIntegratedWithRoutineCaseNotificationData.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.ActiveCaseDetection.DataLinkage.ConvertBoolToYesNo()).ToString(), Step_A.ActiveCaseDetection.DetailsOnDataLinkageToTheIndexCase, Step_A.ActiveCaseDetection.NationalLevelOfInvolvement, Step_A.ActiveCaseDetection.SubNationalLevelOfInvolvement, Step_A.ActiveCaseDetection.ServiceDeliveryLevelOfInvolvement, Step_A.ActiveCaseDetection.ChallengesWithReporting));
            eliminationActivities.Add(new EliminationActivityReport(translator.DynamicInvoke(AnalyticalOutputConstants.ReactiveDetection_2_1_3).ToString(), translator.DynamicInvoke(Step_A.ReactiveDetection.ActivityInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.ReactiveDetection.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.ReactiveDetection.DataIntegratedWithRoutineCaseNotificationData.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.ReactiveDetection.DataLinkage.ConvertBoolToYesNo()).ToString(), Step_A.ReactiveDetection.DetailsOnDataLinkageToTheIndexCase, Step_A.ReactiveDetection.NationalLevelOfInvolvement, Step_A.ReactiveDetection.SubNationalLevelOfInvolvement, Step_A.ReactiveDetection.ServiceDeliveryLevelOfInvolvement, Step_A.ReactiveDetection.ChallengesWithReporting));
            eliminationActivities.Add(new EliminationActivityReport(translator.DynamicInvoke(AnalyticalOutputConstants.ProactiveDetection_2_1_3).ToString(), translator.DynamicInvoke(Step_A.ProactiveDetection.ActivityInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(AnalyticalOutputHelper.ConvertBoolToYesNo(Step_A.ProactiveDetection.SurveillanceImplemented)).ToString(), translator.DynamicInvoke(Step_A.ProactiveDetection.DataIntegratedWithRoutineCaseNotificationData.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_A.ProactiveDetection.DataLinkage.ConvertBoolToYesNo()).ToString(), Step_A.ProactiveDetection.DetailsOnDataLinkageToTheIndexCase, Step_A.ProactiveDetection.NationalLevelOfInvolvement, Step_A.ProactiveDetection.SubNationalLevelOfInvolvement, Step_A.ProactiveDetection.ServiceDeliveryLevelOfInvolvement, Step_A.ProactiveDetection.ChallengesWithReporting));
                        
            return eliminationActivities;
        }

        /// <summary>
        /// Get list of other malaria control strategies for tab b
        /// </summary>
        /// <returns>List of other malaria control strategies</returns>
        private List<OtherMalariaControlStrategyReport> GetOtherMalariaControlStrategyReport(Delegate translator)
        {
            List<OtherMalariaControlStrategyReport> otherMalariaControlStrategies = new List<OtherMalariaControlStrategyReport>();

            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.ChemopreventionInPregnantWomen_2_1_3).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionInPregnantWomen.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionInPregnantWomen.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionInPregnantWomen.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.ChemoPreventionInPregnantWomen.MethodOfIntegration, Step_B.ChemoPreventionInPregnantWomen.DataLinkage, Step_B.ChemoPreventionInPregnantWomen.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.ChemopreventionInInfancy_2_1_3).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionInInfancy.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionInInfancy.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionInInfancy.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.ChemoPreventionInInfancy.MethodOfIntegration, Step_B.ChemoPreventionInInfancy.DataLinkage, Step_B.ChemoPreventionInInfancy.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.ChemopreventionSMC_2_1_3).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionSMC.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionSMC.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionSMC.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.ChemoPreventionSMC.MethodOfIntegration, Step_B.ChemoPreventionSMC.DataLinkage, Step_B.ChemoPreventionSMC.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.ChemopreventionMDA_2_1_3).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionMDA.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionMDA.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.ChemoPreventionMDA.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.ChemoPreventionMDA.MethodOfIntegration, Step_B.ChemoPreventionMDA.DataLinkage, Step_B.ChemoPreventionMDA.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.VectorControlRoutineChannel_2_1_3).ToString(), translator.DynamicInvoke(Step_B.VectorControlRoutineChannel.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.VectorControlRoutineChannel.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.VectorControlRoutineChannel.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.VectorControlRoutineChannel.MethodOfIntegration, Step_B.VectorControlRoutineChannel.DataLinkage, Step_B.VectorControlRoutineChannel.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.VectorControlsMassCampaigns_2_1_3).ToString(), translator.DynamicInvoke(Step_B.VectorControlMassCampaign.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.VectorControlMassCampaign.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.VectorControlMassCampaign.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.VectorControlMassCampaign.MethodOfIntegration, Step_B.VectorControlMassCampaign.DataLinkage, Step_B.VectorControlMassCampaign.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.VectorControlIRS_2_1_3).ToString(), translator.DynamicInvoke(Step_B.VectorControlIRS.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.VectorControlIRS.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.VectorControlIRS.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.VectorControlIRS.MethodOfIntegration, Step_B.VectorControlIRS.DataLinkage, Step_B.VectorControlIRS.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.VectorControlLSM_2_1_3).ToString(), translator.DynamicInvoke(Step_B.VectorControlLSM.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.VectorControlLSM.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.VectorControlLSM.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.VectorControlLSM.MethodOfIntegration, Step_B.VectorControlLSM.DataLinkage, Step_B.VectorControlLSM.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.DrugEfficacy_2_1_3).ToString(), translator.DynamicInvoke(Step_B.DrugEfficacy.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.DrugEfficacy.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.DrugEfficacy.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.DrugEfficacy.MethodOfIntegration, Step_B.DrugEfficacy.DataLinkage, Step_B.DrugEfficacy.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.GenomicSurveillance_2_1_3).ToString(), translator.DynamicInvoke(Step_B.GenomicSurveillance.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.GenomicSurveillance.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.GenomicSurveillance.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.GenomicSurveillance.MethodOfIntegration, Step_B.GenomicSurveillance.DataLinkage, Step_B.GenomicSurveillance.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.EntomologicalSurveillance_2_1_3).ToString(), translator.DynamicInvoke(Step_B.EntomologicalSurveillance.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.EntomologicalSurveillance.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.EntomologicalSurveillance.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.EntomologicalSurveillance.MethodOfIntegration, Step_B.EntomologicalSurveillance.DataLinkage, Step_B.EntomologicalSurveillance.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.CommodityTracking_2_1_3).ToString(), translator.DynamicInvoke(Step_B.CommodityTracking.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.CommodityTracking.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.CommodityTracking.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.CommodityTracking.MethodOfIntegration, Step_B.CommodityTracking.DataLinkage, Step_B.CommodityTracking.DetailsOnDataLinkageToTheIndexCase, true));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.VitalRegistrationSystem_2_1_3).ToString(), translator.DynamicInvoke(Step_B.VitalRegistrationSystem.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.VitalRegistrationSystem.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.VitalRegistrationSystem.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.VitalRegistrationSystem.MethodOfIntegration, Step_B.VitalRegistrationSystem.DataLinkage, Step_B.VitalRegistrationSystem.DetailsOnDataLinkageToTheIndexCase, false));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.LaboratoryData_2_1_3).ToString(), translator.DynamicInvoke(Step_B.LaboratoryData.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.LaboratoryData.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.LaboratoryData.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.LaboratoryData.MethodOfIntegration, Step_B.LaboratoryData.DataLinkage, Step_B.LaboratoryData.DetailsOnDataLinkageToTheIndexCase, false));
            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(Step_B.SpecifyOther.OtherStrategy, translator.DynamicInvoke(Step_B.SpecifyOther.StrategyInPlace.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.SpecifyOther.SurveillanceImplemented.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Step_B.SpecifyOther.DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem.ConvertBoolToYesNo()).ToString(), Step_B.SpecifyOther.MethodOfIntegration, Step_B.SpecifyOther.DataLinkage, Step_B.SpecifyOther.DetailsOnDataLinkageToTheIndexCase, false));

            return otherMalariaControlStrategies;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        ///<param name="translator">Delegate object which is used for translation</param>
        /// <param name="strategyId">Contains strategy id</param>  
        /// <returns>Analytical output indicator response of excel export for indicator 2.1.3</returns>
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator, Guid strategyId)
        {
            List<EliminationActivityReport> eliminationActivities = GetEliminationActivityReport(translator);

            List<OtherMalariaControlStrategyReport> otherMalariaControlStrategies = GetOtherMalariaControlStrategyReport(translator);
            string indicatorOptionYes = translator.DynamicInvoke(Common.IndicatorYes).ToString();
           
            int strategyInPlace = otherMalariaControlStrategies.Count(d => d.StrategyInPlace == indicatorOptionYes && d.IsConsiderForCalculation == true);
            int surveillanceImplemented = otherMalariaControlStrategies.Count(d => d.SurveillanceImplemented == indicatorOptionYes && d.IsConsiderForCalculation == true);

            int activityInPlace = eliminationActivities.Count(d => d.ActivityInPlace == indicatorOptionYes);
            int eliminationSurveillanceImplemented = eliminationActivities.Count(d => d.SurveillanceImplemented == indicatorOptionYes);

            eliminationActivities.Add(new EliminationActivityReport(translator.DynamicInvoke(AnalyticalOutputConstants.EliminationActivitiesFooterTitle_2_1_3).ToString(),
                AnalyticalOutputHelper.CalculatePercentage(activityInPlace, eliminationSurveillanceImplemented).ToString() + "%",
                null, null, null, null, null, null, null, null));

            TableResponse eliminationActivityTable = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(EliminationActivityReport), eliminationActivities, translator);

            otherMalariaControlStrategies.Add(new OtherMalariaControlStrategyReport(translator.DynamicInvoke(AnalyticalOutputConstants.OtherMalariaTableFooterTitle_2_1_3).ToString()
            , AnalyticalOutputHelper.CalculatePercentage(strategyInPlace, surveillanceImplemented).ToString() + "%",
            null, null, null, null, null, false));

            List<TableResponse> responseData = new List<TableResponse>();

            eliminationActivityTable.TabId = 0;
            eliminationActivityTable.HasCalculation = true;

            eliminationActivityTable.TabName = translator.DynamicInvoke(AnalyticalOutputConstants.EliminationActivities_2_1_3).ToString();

            responseData.Add(eliminationActivityTable);

            TableResponse otherMalarialControlTable = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(OtherMalariaControlStrategyReport), otherMalariaControlStrategies, translator);

            otherMalarialControlTable.TabId = 1;
            otherMalarialControlTable.HasCalculation = true;

            otherMalarialControlTable.TabName = translator.DynamicInvoke(AnalyticalOutputConstants.OtherMalariaControlStrategies_2_1_3).ToString();

            responseData.Add(otherMalarialControlTable);

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto();

            // Show both Part A and Part B for all strategies (Elimination, Burden Reduction, etc.)
            response.Response = responseData;
            response.Type = (int)AnalyticalOutputType.TabWithTable;

            return response;
        }

        /// <summary>
        /// Prepares analytical report for the indicator
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains indicator sequence</param>  
        /// <param name="strategyId">Contains strategy id</param>  
        /// <returns>Indicator 2.1.3 response in the form of data table</returns>
        public List<TabularDataInputModel> BuildAnalyticalReport(Delegate translator, string indicatorSequence, Guid strategyId)
        {
            List<TabularDataInputModel> tabularData = new List<TabularDataInputModel>();

            List<EliminationActivityReport> eliminationActivities = GetEliminationActivityReport(translator);
            string indicatorOptionYes = translator.DynamicInvoke(Common.IndicatorYes).ToString();
            int activityInPlace = eliminationActivities.Count(d => d.ActivityInPlace == indicatorOptionYes);
            int eliminationSurveillanceImplemented = eliminationActivities.Count(d => d.SurveillanceImplemented == indicatorOptionYes);

            List<OtherMalariaControlStrategyReport> otherMalariaControlStrategies = GetOtherMalariaControlStrategyReport(translator);

            int strategyInPlace = otherMalariaControlStrategies.Count(d => d.StrategyInPlace == indicatorOptionYes && d.IsConsiderForCalculation == true);
            int surveillanceImplemented = otherMalariaControlStrategies.Count(d => d.SurveillanceImplemented == indicatorOptionYes && d.IsConsiderForCalculation == true);

            DataSet eliminationActivityDataSet = new DataSet();

            DataTable eliminationActivityTable = AnalyticalOutputHelper.GetDataTable(typeof(EliminationActivityReport), eliminationActivities, $"{indicatorSequence}_Elimination Activities", translator);

            eliminationActivityTable.Rows.Add();

            eliminationActivityTable.Rows.Add(translator.DynamicInvoke(AnalyticalOutputConstants.EliminationActivitiesFooterTitle_2_1_3), AnalyticalOutputHelper.CalculatePercentage(activityInPlace, eliminationSurveillanceImplemented) + "%");

            eliminationActivityDataSet.Tables.Add(eliminationActivityTable);

            DataSet otherMalarialControlDataSet = new DataSet();

            DataTable otherMalarialControlTable = AnalyticalOutputHelper.GetDataTable(typeof(OtherMalariaControlStrategyReport), otherMalariaControlStrategies, $"{indicatorSequence}", translator);

            otherMalarialControlTable.Rows.Add();

            otherMalarialControlTable.Rows.Add(translator.DynamicInvoke(AnalyticalOutputConstants.OtherMalariaTableFooterTitle_2_1_3), AnalyticalOutputHelper.CalculatePercentage(strategyInPlace, surveillanceImplemented) + "%");

            otherMalarialControlDataSet.Tables.Add(otherMalarialControlTable);

            // Show both Part A and Part B for all strategies (Elimination, Burden Reduction, etc.)
            tabularData.Add(new TabularDataInputModel { SheetName = eliminationActivityTable.TableName, Tables = eliminationActivityDataSet });
            tabularData.Add(new TabularDataInputModel { SheetName = otherMalarialControlTable.TableName, Tables = otherMalarialControlDataSet });

            return tabularData;
        }
    }

    /// <summary>
    /// Contains tab data for elimination activity
    /// </summary>
    public class Step_A_Response
    {
        public EliminationActivity CaseInvestigation { get; set; }

        public EliminationActivity CaseClassification { get; set; }

        public EliminationActivity FocusInvestigation { get; set; }

        public EliminationActivity FocusClassificiation { get; set; }

        public EliminationActivity ActiveCaseDetection { get; set; }

        public EliminationActivity ReactiveDetection { get; set; }

        public EliminationActivity ProactiveDetection { get; set; }
    }

    /// <summary>
    /// Contains tab data for elimination activity
    /// </summary>
    public class EliminationActivity
    {
        public bool? ActivityInPlace { get; set; }

        public bool? SurveillanceImplemented { get; set; }

        public bool? DataIntegratedWithRoutineCaseNotificationData { get; set; }

        public bool? DataLinkage { get; set; }

        public string DetailsOnDataLinkageToTheIndexCase { get; set; }

        public string NationalLevelOfInvolvement { get; set; }

        public string SubNationalLevelOfInvolvement { get; set; }

        public string ServiceDeliveryLevelOfInvolvement { get; set; }

        public string ChallengesWithReporting { get; set; }
    }

    /// <summary>
    /// Contains tab data for elimination activity for report
    /// </summary>
    public class EliminationActivityReport
    {
        [TableColumn(Name = "Name", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.EliminationActivity", Width = Common.Width300, Order = 1)]
        public string Name { get; set; }

        [TableColumn(Name = "ActivityInPlace", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.ActivityInPlace", Width = Common.Width200, Order = 2)]
        public string ActivityInPlace { get; set; }

        [TableColumn(Name = "SurveillanceImplemented", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.SurveillanceImplemented", Width = Common.Width200, Order = 3)]
        public string SurveillanceImplemented { get; set; }

        [TableColumn(Name = "DataIntegratedWithRoutineCaseNotificationData", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.DataIntegrated", Width = Common.Width200, Order = 4)]
        public string DataIntegratedWithRoutineCaseNotificationData { get; set; }

        [TableColumn(Name = "DataLinkage", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.DataLinkage", Width = Common.Width200, Order = 5)]
        public string DataLinkage { get; set; }

        [TableColumn(Name = "DetailsOnDataLinkageToTheIndexCase", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.DetailsOfData", Width = Common.Width300, Order = 6)]
        public string DetailsOnDataLinkageToTheIndexCase { get; set; }

        [TableColumn(Name = "NationalLevelOfInvolvement", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.NationalLevel", Width = Common.Width300, Order = 7)]
        public string NationalLevelOfInvolvement { get; set; }

        [TableColumn(Name = "SubNationalLevelOfInvolvement", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.SubNationalLevel", Width = Common.Width300, Order = 8)]
        public string SubNationalLevelOfInvolvement { get; set; }

        [TableColumn(Name = "SubNationalLevelOfInvolvement", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.ServiceDelivery", Width = Common.Width300, Order = 9)]
        public string ServiceDeliveryLevelOfInvolvement { get; set; }

        [TableColumn(Name = "ChallengesWithReporting", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.Challenges", Width = Common.Width300, Order = 10)]
        public string ChallengesWithReporting { get; set; }

        public EliminationActivityReport(string name, string activityInPlace, string surveillanceImplemented, string dataIntegratedWithRoutineCaseNotificationData, string dataLinkage, string detailsOnDataLinkageToTheIndexCase, string nationalLevelOfInvolvement, string subNationalLevelOfInvolvement, string serviceDeliveryLevelOfInvolvement, string challengesWithReporting)
        {
            Name = name;
            ActivityInPlace = activityInPlace;
            SurveillanceImplemented = surveillanceImplemented;
            DataIntegratedWithRoutineCaseNotificationData = dataIntegratedWithRoutineCaseNotificationData;
            DataLinkage = dataLinkage;
            DetailsOnDataLinkageToTheIndexCase = detailsOnDataLinkageToTheIndexCase;
            NationalLevelOfInvolvement = nationalLevelOfInvolvement;
            SubNationalLevelOfInvolvement = subNationalLevelOfInvolvement;
            ServiceDeliveryLevelOfInvolvement = serviceDeliveryLevelOfInvolvement;
            ChallengesWithReporting = challengesWithReporting;
        }
    }

    /// <summary>
    /// Contains tab data for other malaria control strategy
    /// </summary>
    public class Step_B_Response
    {
        public OtherMalariaControlStrategy ChemoPreventionInPregnantWomen { get; set; }

        public OtherMalariaControlStrategy ChemoPreventionInInfancy { get; set; }

        public OtherMalariaControlStrategy ChemoPreventionSMC { get; set; }

        public OtherMalariaControlStrategy ChemoPreventionMDA { get; set; }

        public OtherMalariaControlStrategy VectorControlRoutineChannel { get; set; }

        public OtherMalariaControlStrategy VectorControlMassCampaign { get; set; }

        public OtherMalariaControlStrategy VectorControlIRS { get; set; }

        public OtherMalariaControlStrategy VectorControlLSM { get; set; }

        public OtherMalariaControlStrategy DrugEfficacy { get; set; }

        public OtherMalariaControlStrategy GenomicSurveillance { get; set; }

        public OtherMalariaControlStrategy EntomologicalSurveillance { get; set; }

        public OtherMalariaControlStrategy CommodityTracking { get; set; }

        public OtherMalariaControlStrategy VitalRegistrationSystem { get; set; }

        public OtherMalariaControlStrategy LaboratoryData { get; set; }

        public OtherMalariaControlStrategy SpecifyOther { get; set; }
    }

    /// <summary>
    /// Contains tab data for other malaria control strategy 
    /// </summary>
    public class OtherMalariaControlStrategy
    {
        public string OtherStrategy { get; set; }
        public bool? StrategyInPlace { get; set; }
        public bool? SurveillanceImplemented { get; set; }
        public bool? DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem { get; set; }
        public string MethodOfIntegration { get; set; }
        public string DataLinkage { get; set; }
        public string DetailsOnDataLinkageToTheIndexCase { get; set; }
    }

    /// <summary>
    /// Contains tab data for other malaria control strategy for report
    /// </summary>
    public class OtherMalariaControlStrategyReport
    {
        [TableColumn(Name = "OtherStrategy", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.MalariaControlStrategy", Width = Common.Width300, Order = 1)]
        public string OtherStrategy { get; set; }

        [TableColumn(Name = "StrategyInPlace", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.StrategyInPlace", Width = Common.Width200, Order = 2)]
        public string StrategyInPlace { get; set; }

        [TableColumn(Name = "SurveillanceImplemented", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.SurveillanceImplemented", Width = Common.Width200, Order = 3)]
        public string SurveillanceImplemented { get; set; }

        [TableColumn(Name = "DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.DataIntegratedSurveillance", Width = Common.Width200, Order = 4)]
        public string DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem { get; set; }

        [TableColumn(Name = "MethodOfIntegration", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.MethodOfIntegration", Width = Common.Width300, Order = 5)]
        public string MethodOfIntegration { get; set; }

        [TableColumn(Name = "DataLinkage", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.DataLinkage", Width = Common.Width300, Order = 6)]
        public string DataLinkage { get; set; }

        [TableColumn(Name = "DetailsOnDataLinkageToTheIndexCase", TranslationKey = "DRObjective_2_Responses.Indicator_2_1_3.DetailsOfData", Width = Common.Width300, Order = 7)]
        public string DetailsOnDataLinkageToTheIndexCase { get; set; }

        public bool IsConsiderForCalculation { get; set; }

        public OtherMalariaControlStrategyReport(string otherStrategy, string strategyInPlace, string surveillanceImplemented, string dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem, string methodOfIntegration, string dataLinkage, string detailsOnDataLinkageToTheIndexCase, bool isConsiderForCalculation)
        {
            OtherStrategy = otherStrategy;
            StrategyInPlace = strategyInPlace;
            SurveillanceImplemented = surveillanceImplemented;
            DataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem = dataIntegratedWithThePrimaryMalariaCaseSurveillanceSystem;
            MethodOfIntegration = methodOfIntegration;
            DataLinkage = dataLinkage;
            DetailsOnDataLinkageToTheIndexCase = detailsOnDataLinkageToTheIndexCase;
            IsConsiderForCalculation = isConsiderForCalculation;
        }

    }
}
