﻿using FluentValidation;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_3.Indicator_3_4_2
{
    /// <summary>
    /// Contains validation rules for indicator 3.4.2
    /// </summary>
    class Response_1_Validator : AbstractValidator<Response_1>
    {
        public Response_1_Validator()
        {
            string validationMsg = "Please select 'Yes' or 'No' for all 'Indicator monitored in routine outputs' for indicators list";

            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' is false
            When(x => x.CannotBeAssessed == false, () =>
            {
                //Validate P.vivax cases question
                RuleFor(x => x.HasPVivaxCases)
                .NotNull().WithMessage("Please select 'Yes' or 'No' for 'Do you have any P.vivax cases?'");

                //Total variables are 43, so count is checked with 43
                RuleFor(x => x).Must(k => k.TransmitMalariaIndicators.Count == k.CheckListIndicatorsCount).WithMessage(validationMsg);

                RuleForEach(x => x.TransmitMalariaIndicators).
                Must(k => k.IndicatorMonitored != null).WithMessage(validationMsg);
            });
        }
    }
}
